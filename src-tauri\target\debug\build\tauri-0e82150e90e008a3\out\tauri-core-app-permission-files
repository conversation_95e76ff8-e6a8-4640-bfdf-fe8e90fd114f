["\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\app\\autogenerated\\commands\\app_hide.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\app\\autogenerated\\commands\\app_show.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\app\\autogenerated\\commands\\default_window_icon.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\app\\autogenerated\\commands\\fetch_data_store_identifiers.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\app\\autogenerated\\commands\\identifier.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\app\\autogenerated\\commands\\name.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\app\\autogenerated\\commands\\remove_data_store.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\app\\autogenerated\\commands\\set_app_theme.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\app\\autogenerated\\commands\\set_dock_visibility.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\app\\autogenerated\\commands\\tauri_version.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\app\\autogenerated\\commands\\version.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\app\\autogenerated\\default.toml"]