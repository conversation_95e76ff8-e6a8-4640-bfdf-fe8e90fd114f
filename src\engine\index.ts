/**
 * 网格变形引擎入口文件
 */

// 导出主引擎类
export { MeshEngine } from './MeshEngine';

// 导出核心组件
export { ControlPoint } from './core/ControlPoint';
export { GridMesh } from './core/GridMesh';
export { InteractionManager } from './core/InteractionManager';
export { EventEmitter } from './core/EventEmitter';

// 导出工具类
export { MathUtils } from './utils/math';

// 导出类型定义
export type {
  Point2D,
  Size,
  Bounds,
  GridConfig,
  ControlPointConfig,
  BrushConfig,
  MeshEngineConfig,
  ControlPointData,
  MeshVertex,
  Triangle,
  GridMeshData,
  InteractionState,
  DeformationOptions,
  MeshEngineEvents,
  EventCallback
} from './types';

// 导出枚举
export { InteractionMode } from './types';

// 导出默认配置
export const DEFAULT_CONFIG = {
  grid: {
    rows: 10,
    cols: 10,
    showGrid: true,
    gridColor: 0x00ff00,
    gridAlpha: 0.3
  },
  controlPoint: {
    radius: 8,
    color: 0xff0000,
    hoverColor: 0xff6666,
    selectedColor: 0xffff00,
    strokeWidth: 2,
    strokeColor: 0x000000
  },
  backgroundColor: 0x222222,
  enableInteraction: true
};

// 创建引擎实例的便捷函数
export function createMeshEngine(container: HTMLElement, options: Partial<MeshEngineConfig> = {}): MeshEngine {
  const config: MeshEngineConfig = {
    container,
    width: container.clientWidth || 800,
    height: container.clientHeight || 600,
    backgroundColor: DEFAULT_CONFIG.backgroundColor,
    grid: { ...DEFAULT_CONFIG.grid, ...options.grid },
    controlPoint: { ...DEFAULT_CONFIG.controlPoint, ...options.controlPoint },
    enableInteraction: DEFAULT_CONFIG.enableInteraction,
    ...options
  };

  return new MeshEngine(config);
}

// 版本信息
export const VERSION = '1.0.0';
