/**
 * 网格类 - 管理图片的网格变形
 */

import * as PIXI from 'pixi.js';
import type { Point2D, GridMeshData, MeshVertex, Triangle, GridConfig } from '../types';
import { MathUtils } from '../utils/math';

export class GridMesh extends PIXI.Container {
  private texture: PIXI.Texture;
  private meshData: GridMeshData;
  private mesh: PIXI.Mesh | null = null;
  private gridLines: PIXI.Graphics;
  private config: GridConfig;
  private imageSize: { width: number; height: number };

  constructor(texture: PIXI.Texture, config: GridConfig, imageSize: { width: number; height: number }) {
    super();

    this.texture = texture;
    this.config = config;
    this.imageSize = imageSize;

    // 创建网格线容器
    this.gridLines = new PIXI.Graphics();
    this.addChild(this.gridLines);

    // 初始化网格数据
    this.meshData = this.createGridMeshData();
    
    // 创建网格
    this.createMesh();
    
    // 绘制网格线
    this.updateGridLines();
  }

  /**
   * 创建网格数据
   */
  private createGridMeshData(): GridMeshData {
    const { rows, cols } = this.config;
    const { width, height } = this.imageSize;
    
    const vertices: MeshVertex[] = [];
    const triangles: Triangle[] = [];

    // 创建顶点
    for (let row = 0; row <= rows; row++) {
      for (let col = 0; col <= cols; col++) {
        const x = (col / cols) * width;
        const y = (row / rows) * height;
        const u = col / cols;
        const v = row / rows;

        vertices.push({
          position: { x, y },
          uv: { x: u, y: v },
          originalPosition: { x, y }
        });
      }
    }

    // 创建三角形
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const topLeft = row * (cols + 1) + col;
        const topRight = topLeft + 1;
        const bottomLeft = (row + 1) * (cols + 1) + col;
        const bottomRight = bottomLeft + 1;

        // 第一个三角形 (左上, 右上, 左下)
        triangles.push({
          vertices: [topLeft, topRight, bottomLeft]
        });

        // 第二个三角形 (右上, 右下, 左下)
        triangles.push({
          vertices: [topRight, bottomRight, bottomLeft]
        });
      }
    }

    return {
      vertices,
      triangles,
      rows,
      cols
    };
  }

  /**
   * 创建PIXI网格
   */
  private createMesh(): void {
    const vertices: number[] = [];
    const uvs: number[] = [];
    const indices: number[] = [];

    // 构建顶点数组
    this.meshData.vertices.forEach(vertex => {
      vertices.push(vertex.position.x, vertex.position.y);
      uvs.push(vertex.uv.x, vertex.uv.y);
    });

    // 构建索引数组
    this.meshData.triangles.forEach(triangle => {
      indices.push(...triangle.vertices);
    });

    // 创建几何体
    const geometry = new PIXI.Geometry()
      .addAttribute('aVertexPosition', vertices, 2)
      .addAttribute('aTextureCoord', uvs, 2)
      .addIndex(indices);

    // 创建材质
    const material = new PIXI.MeshMaterial(this.texture);

    // 移除旧网格
    if (this.mesh) {
      this.removeChild(this.mesh);
      this.mesh.destroy();
    }

    // 创建新网格
    this.mesh = new PIXI.Mesh(geometry, material);
    this.addChildAt(this.mesh, 0); // 添加到网格线下方
  }

  /**
   * 更新网格
   */
  public updateMesh(): void {
    if (!this.mesh) return;

    const vertices: number[] = [];
    
    // 更新顶点位置
    this.meshData.vertices.forEach(vertex => {
      vertices.push(vertex.position.x, vertex.position.y);
    });

    // 更新几何体
    const geometry = this.mesh.geometry;
    geometry.getBuffer('aVertexPosition').update(vertices);
    
    // 更新网格线
    this.updateGridLines();
  }

  /**
   * 更新网格线显示
   */
  private updateGridLines(): void {
    this.gridLines.clear();

    if (!this.config.showGrid) return;

    this.gridLines.lineStyle(1, this.config.gridColor, this.config.gridAlpha);

    const { rows, cols } = this.config;

    // 绘制水平线
    for (let row = 0; row <= rows; row++) {
      const startIndex = row * (cols + 1);
      const endIndex = startIndex + cols;
      
      const startVertex = this.meshData.vertices[startIndex];
      const endVertex = this.meshData.vertices[endIndex];
      
      this.gridLines.moveTo(startVertex.position.x, startVertex.position.y);
      this.gridLines.lineTo(endVertex.position.x, endVertex.position.y);
    }

    // 绘制垂直线
    for (let col = 0; col <= cols; col++) {
      for (let row = 0; row < rows; row++) {
        const currentIndex = row * (cols + 1) + col;
        const nextIndex = (row + 1) * (cols + 1) + col;
        
        const currentVertex = this.meshData.vertices[currentIndex];
        const nextVertex = this.meshData.vertices[nextIndex];
        
        this.gridLines.moveTo(currentVertex.position.x, currentVertex.position.y);
        this.gridLines.lineTo(nextVertex.position.x, nextVertex.position.y);
      }
    }
  }

  /**
   * 应用变形
   */
  public applyDeformation(controlPoints: Array<{ original: Point2D; current: Point2D; influence: number }>): void {
    // 使用RBF算法进行变形
    MathUtils.deformMeshRBF(this.meshData.vertices, controlPoints);
    
    // 更新网格显示
    this.updateMesh();
  }

  /**
   * 重置网格到原始状态
   */
  public reset(): void {
    this.meshData.vertices.forEach(vertex => {
      vertex.position.x = vertex.originalPosition.x;
      vertex.position.y = vertex.originalPosition.y;
    });
    
    this.updateMesh();
  }

  /**
   * 设置网格配置
   */
  public setConfig(config: Partial<GridConfig>): void {
    this.config = { ...this.config, ...config };
    
    // 如果行列数改变，重新创建网格
    if (config.rows !== undefined || config.cols !== undefined) {
      this.meshData = this.createGridMeshData();
      this.createMesh();
    }
    
    this.updateGridLines();
  }

  /**
   * 获取网格数据
   */
  public getMeshData(): GridMeshData {
    return {
      ...this.meshData,
      vertices: this.meshData.vertices.map(v => ({
        ...v,
        position: { ...v.position },
        uv: { ...v.uv },
        originalPosition: { ...v.originalPosition }
      }))
    };
  }

  /**
   * 获取指定位置最近的网格顶点
   */
  public getNearestVertex(position: Point2D): { vertex: MeshVertex; index: number; distance: number } | null {
    let nearestVertex: MeshVertex | null = null;
    let nearestIndex = -1;
    let minDistance = Infinity;

    this.meshData.vertices.forEach((vertex, index) => {
      const distance = MathUtils.distance(position, vertex.position);
      if (distance < minDistance) {
        minDistance = distance;
        nearestVertex = vertex;
        nearestIndex = index;
      }
    });

    return nearestVertex ? { vertex: nearestVertex, index: nearestIndex, distance: minDistance } : null;
  }

  /**
   * 销毁网格
   */
  public destroy(): void {
    if (this.mesh) {
      this.mesh.destroy();
    }
    this.gridLines.destroy();
    super.destroy();
  }
}
