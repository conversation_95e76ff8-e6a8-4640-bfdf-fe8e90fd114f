# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-version"
description = "Enables the version command without any pre-configured scope."
commands.allow = ["version"]

[[permission]]
identifier = "deny-version"
description = "Denies the version command without any pre-configured scope."
commands.deny = ["version"]
