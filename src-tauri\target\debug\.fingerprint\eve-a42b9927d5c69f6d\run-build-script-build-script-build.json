{"rustc": 18201530525194187439, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10197488054225886119, "build_script_build", false, 12838962572857404019], [10755362358622467486, "build_script_build", false, 2604541221486670530], [17218623086136245857, "build_script_build", false, 3129551010173680987]], "local": [{"RerunIfChanged": {"output": "debug\\build\\eve-a42b9927d5c69f6d\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}