["\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\path\\autogenerated\\default.toml"]