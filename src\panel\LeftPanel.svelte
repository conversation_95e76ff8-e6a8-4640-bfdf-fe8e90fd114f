<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { GridConfig, ControlPointConfig, BrushConfig } from '../engine';

  const dispatch = createEventDispatcher();

  export let gridConfig: GridConfig = {
    rows: 10,
    cols: 10,
    showGrid: true,
    gridColor: 0x00ff00,
    gridAlpha: 0.3
  };

  export let controlPointConfig: ControlPointConfig = {
    radius: 8,
    color: 0xff0000,
    hoverColor: 0xff6666,
    selectedColor: 0xffff00,
    strokeWidth: 2,
    strokeColor: 0x000000
  };

  export let brushConfig: BrushConfig = {
    size: 30,
    strength: 0.5,
    showWeightMap: false
  };

  export let hasImage: boolean = false;

  function handleGridConfigChange() {
    dispatch('grid-config-change', { config: gridConfig });
  }

  function handleControlPointConfigChange() {
    dispatch('control-point-config-change', { config: controlPointConfig });
  }

  function handleBrushConfigChange() {
    dispatch('brush-config-change', { config: brushConfig });
  }

  function handleClearWeights() {
    dispatch('clear-weights');
  }

  function colorToHex(color: number): string {
    return '#' + color.toString(16).padStart(6, '0');
  }

  function hexToColor(hex: string): number {
    return parseInt(hex.replace('#', ''), 16);
  }

  $: gridColorHex = colorToHex(gridConfig.gridColor);
  $: controlPointColorHex = colorToHex(controlPointConfig.color);
  $: hoverColorHex = colorToHex(controlPointConfig.hoverColor);
  $: selectedColorHex = colorToHex(controlPointConfig.selectedColor);
  $: strokeColorHex = colorToHex(controlPointConfig.strokeColor);
</script>

<div class="left-panel">
  <div class="panel-header">
    <h2>设置面板</h2>
  </div>

  <div class="panel-content">
    <!-- 网格设置 -->
    <div class="setting-group">
      <h3>网格设置</h3>

      <div class="setting-item">
        <label for="grid-rows">行数</label>
        <input
          id="grid-rows"
          type="range"
          min="3"
          max="20"
          bind:value={gridConfig.rows}
          on:input={handleGridConfigChange}
          disabled={!hasImage}
        />
        <span class="value">{gridConfig.rows}</span>
      </div>

      <div class="setting-item">
        <label for="grid-cols">列数</label>
        <input
          id="grid-cols"
          type="range"
          min="3"
          max="20"
          bind:value={gridConfig.cols}
          on:input={handleGridConfigChange}
          disabled={!hasImage}
        />
        <span class="value">{gridConfig.cols}</span>
      </div>

      <div class="setting-item">
        <label for="show-grid">显示网格</label>
        <input
          id="show-grid"
          type="checkbox"
          bind:checked={gridConfig.showGrid}
          on:change={handleGridConfigChange}
          disabled={!hasImage}
        />
      </div>

      {#if gridConfig.showGrid}
        <div class="setting-item">
          <label for="grid-color">网格颜色</label>
          <input
            id="grid-color"
            type="color"
            value={gridColorHex}
            on:input={(e) => {
              gridConfig.gridColor = hexToColor(e.currentTarget.value);
              handleGridConfigChange();
            }}
            disabled={!hasImage}
          />
        </div>

        <div class="setting-item">
          <label for="grid-alpha">网格透明度</label>
          <input
            id="grid-alpha"
            type="range"
            min="0"
            max="1"
            step="0.1"
            bind:value={gridConfig.gridAlpha}
            on:input={handleGridConfigChange}
            disabled={!hasImage}
          />
          <span class="value">{gridConfig.gridAlpha.toFixed(1)}</span>
        </div>
      {/if}
    </div>

    <!-- 控制点设置 -->
    <div class="setting-group">
      <h3>控制点设置</h3>

      <div class="setting-item">
        <label for="point-radius">半径</label>
        <input
          id="point-radius"
          type="range"
          min="4"
          max="20"
          bind:value={controlPointConfig.radius}
          on:input={handleControlPointConfigChange}
          disabled={!hasImage}
        />
        <span class="value">{controlPointConfig.radius}px</span>
      </div>

      <div class="setting-item">
        <label for="point-color">默认颜色</label>
        <input
          id="point-color"
          type="color"
          value={controlPointColorHex}
          on:input={(e) => {
            controlPointConfig.color = hexToColor(e.currentTarget.value);
            handleControlPointConfigChange();
          }}
          disabled={!hasImage}
        />
      </div>

      <div class="setting-item">

    <!-- 权重笔刷设置 -->
    <div class="setting-group">
      <h3>权重笔刷</h3>

      <div class="setting-item">
        <label for="brush-size">笔刷大小</label>
        <input
          id="brush-size"
          type="range"
          min="10"
          max="100"
          bind:value={brushConfig.size}
          on:input={handleBrushConfigChange}
          disabled={!hasImage}
        />
        <span class="value">{brushConfig.size}px</span>
      </div>

      <div class="setting-item">
        <label for="brush-strength">笔刷强度</label>
        <input
          id="brush-strength"
          type="range"
          min="0.1"
          max="1.0"
          step="0.1"
          bind:value={brushConfig.strength}
          on:input={handleBrushConfigChange}
          disabled={!hasImage}
        />
        <span class="value">{brushConfig.strength.toFixed(1)}</span>
      </div>

      <div class="setting-item">
        <label>
          <input
            type="checkbox"
            bind:checked={brushConfig.showWeightMap}
            on:change={handleBrushConfigChange}
            disabled={!hasImage}
          />
          显示权重图
        </label>
      </div>

      <div class="setting-item">
        <button class="btn btn-secondary" on:click={handleClearWeights} disabled={!hasImage}>
          清除权重
        </button>
      </div>
    </div>
        <label for="hover-color">悬停颜色</label>
        <input
          id="hover-color"
          type="color"
          value={hoverColorHex}
          on:input={(e) => {
            controlPointConfig.hoverColor = hexToColor(e.currentTarget.value);
            handleControlPointConfigChange();
          }}
          disabled={!hasImage}
        />
      </div>

      <div class="setting-item">
        <label for="selected-color">选中颜色</label>
        <input
          id="selected-color"
          type="color"
          value={selectedColorHex}
          on:input={(e) => {
            controlPointConfig.selectedColor = hexToColor(e.currentTarget.value);
            handleControlPointConfigChange();
          }}
          disabled={!hasImage}
        />
      </div>

      <div class="setting-item">
        <label for="stroke-width">描边宽度</label>
        <input
          id="stroke-width"
          type="range"
          min="0"
          max="5"
          bind:value={controlPointConfig.strokeWidth}
          on:input={handleControlPointConfigChange}
          disabled={!hasImage}
        />
        <span class="value">{controlPointConfig.strokeWidth}px</span>
      </div>

      <div class="setting-item">
        <label for="stroke-color">描边颜色</label>
        <input
          id="stroke-color"
          type="color"
          value={strokeColorHex}
          on:input={(e) => {
            controlPointConfig.strokeColor = hexToColor(e.currentTarget.value);
            handleControlPointConfigChange();
          }}
          disabled={!hasImage}
        />
      </div>
    </div>

    <!-- 预设 -->
    <div class="setting-group">
      <h3>预设</h3>
      <div class="preset-buttons">
        <button
          class="preset-btn"
          on:click={() => {
            gridConfig = { rows: 8, cols: 8, showGrid: true, gridColor: 0x00ff00, gridAlpha: 0.3 };
            handleGridConfigChange();
          }}
          disabled={!hasImage}
        >
          低密度网格
        </button>
        <button
          class="preset-btn"
          on:click={() => {
            gridConfig = { rows: 15, cols: 15, showGrid: true, gridColor: 0x0088ff, gridAlpha: 0.4 };
            handleGridConfigChange();
          }}
          disabled={!hasImage}
        >
          高密度网格
        </button>
        <button
          class="preset-btn"
          on:click={() => {
            controlPointConfig = {
              radius: 6,
              color: 0xff4444,
              hoverColor: 0xff8888,
              selectedColor: 0xffff44,
              strokeWidth: 1,
              strokeColor: 0x000000
            };
            handleControlPointConfigChange();
          }}
          disabled={!hasImage}
        >
          小控制点
        </button>
      </div>
    </div>
  </div>
</div>

<style>
  .left-panel {
    width: 280px;
    height: 100%;
    background: #f8fafc;
    border-right: 1px solid #e2e8f0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .panel-header {
    padding: 1rem;
    background: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .panel-header h2 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
  }

  .panel-content {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
  }

  .setting-group {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .setting-group h3 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.5rem;
  }

  .setting-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .setting-item:last-child {
    margin-bottom: 0;
  }

  .setting-item label {
    flex: 1;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4b5563;
  }

  .setting-item input[type="range"] {
    flex: 2;
    height: 4px;
    background: #e5e7eb;
    border-radius: 2px;
    outline: none;
    -webkit-appearance: none;
  }

  .setting-item input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: #3b82f6;
    border-radius: 50%;
    cursor: pointer;
  }

  .setting-item input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: #3b82f6;
    border-radius: 50%;
    cursor: pointer;
    border: none;
  }

  .setting-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #3b82f6;
  }

  .setting-item input[type="color"] {
    width: 40px;
    height: 30px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    cursor: pointer;
  }

  .setting-item input:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .value {
    min-width: 40px;
    font-size: 0.75rem;
    font-weight: 500;
    color: #6b7280;
    text-align: right;
  }

  .preset-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .preset-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    color: #374151;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .preset-btn:hover:not(:disabled) {
    background: #f3f4f6;
    border-color: #9ca3af;
  }

  .preset-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* 滚动条样式 */
  .panel-content::-webkit-scrollbar {
    width: 6px;
  }

  .panel-content::-webkit-scrollbar-track {
    background: #f1f5f9;
  }

  .panel-content::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .panel-content::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
</style>
