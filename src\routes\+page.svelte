<script lang="ts">
  import { onMount } from 'svelte';
  import { PaneGroup, Pane, PaneResizer } from "paneforge";
  import TopPanel from '../panel/TopPanel.svelte';
  import LeftPanel from '../panel/LeftPanel.svelte';
  import RightPanel from '../panel/RightPanel.svelte';
  import BottomPanel from '../panel/BottomPanel.svelte';
  import CenterPanel from '../panel/CenterPanel.svelte';

  import type {
    InteractionMode,
    GridConfig,
    ControlPointConfig,
    MeshEngine,
    ControlPoint
  } from '../engine';
  import { InteractionMode as Mode } from '../engine';

  // 状态管理
  let currentMode: InteractionMode = Mode.NONE;
  let hasImage = false;
  let isProcessing = false;
  let engine: MeshEngine | null = null;
  let allControlPoints: ControlPoint[] = [];
  let selectedControlPoints: ControlPoint[] = [];

  // 配置
  let gridConfig: GridConfig = {
    rows: 10,
    cols: 10,
    showGrid: true,
    gridColor: 0x00ff00,
    gridAlpha: 0.3
  };

  let controlPointConfig: ControlPointConfig = {
    radius: 8,
    color: 0xff4444,
    hoverColor: 0xff8888,
    selectedColor: 0xffff44,
    strokeWidth: 2,
    strokeColor: 0x000000
  };

  // 组件引用
  let centerPanel: CenterPanel;
  let bottomPanel: BottomPanel;

  // 动画相关
  let animationId: number | null = null;
  let swayControlPoints: Array<{
    point: any;
    originalX: number;
    originalY: number;
    amplitude: number;
    frequency: number;
    phase: number
  }> = [];

  // 事件处理函数
  function handleModeChange(event: CustomEvent) {
    currentMode = event.detail.mode;
    addLog('info', `切换到${getModeText(currentMode)}模式`);
  }

  function handleLoadImage(event: CustomEvent) {
    const { url } = event.detail;
    loadImage(url);
  }

  async function loadImage(url: string) {
    if (!centerPanel) return;

    isProcessing = true;
    try {
      await centerPanel.loadImage(url);
      hasImage = true;
      addLog('success', '图片加载成功');
    } catch (error) {
      addLog('error', `图片加载失败: ${error}`);
    } finally {
      isProcessing = false;
    }
  }

  function handleReset() {
    if (centerPanel) {
      centerPanel.resetMesh();
      updateControlPoints();
      addLog('info', '网格已重置');
    }
  }

  function handleExport() {
    if (centerPanel) {
      const data = centerPanel.exportData();
      if (data) {
        // 创建下载链接
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'mesh-deformation.json';
        a.click();
        URL.revokeObjectURL(url);
        addLog('success', '数据导出成功');
      }
    }
  }

  function handleImport() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const data = JSON.parse(e.target?.result as string);
            if (centerPanel) {
              centerPanel.importData(data);
              updateControlPoints();
              addLog('success', '数据导入成功');
            }
          } catch (error) {
            addLog('error', `数据导入失败: ${error}`);
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }

  function handleAutoSway() {
    if (!engine || !hasImage) {
      addLog('warning', '请先加载图片');
      return;
    }

    // 清除现有控制点
    engine.clearAllControlPoints();

    // 自动添加控制点并开始摆动动画
    startSwayAnimation();
    addLog('success', '开始自动摆动动画');
  }

  function startSwayAnimation() {
    if (!engine) return;

    // 停止现有动画
    if (animationId) {
      cancelAnimationFrame(animationId);
      animationId = null;
    }

    // 获取图片的实际位置和尺寸
    const meshBounds = engine.getMeshBounds();
    if (!meshBounds) {
      addLog('error', '无法获取图片边界信息');
      return;
    }

    console.log('Mesh bounds:', meshBounds);

    // 清空现有控制点数组
    swayControlPoints = [];

    // 在图片上创建多行控制点，实现布料效果
    const imageLeft = meshBounds.x;
    const imageTop = meshBounds.y;
    const imageWidth = meshBounds.width;
    const imageHeight = meshBounds.height;

    const rows = 4; // 4行控制点
    const colsPerRow = 6; // 每行6个控制点
    const baseFrequency = 0.003; // 基础摆动频率（更慢）
    const baseAmplitude = 15; // 基础摆动幅度

    for (let row = 0; row < rows; row++) {
      // 从图片中部开始，越往下位置越低
      const rowY = imageTop + imageHeight * (0.4 + (row / rows) * 0.5);

      // 计算这一行的摆动参数
      const rowProgress = row / (rows - 1); // 0 到 1
      const amplitude = baseAmplitude * (1 + rowProgress * 3); // 从上到下幅度增大
      const frequency = baseFrequency * (1 + rowProgress * 0.5); // 频率稍微增加

      for (let col = 0; col < colsPerRow; col++) {
        const colProgress = col / (colsPerRow - 1); // 0 到 1
        const x = imageLeft + imageWidth * (0.1 + colProgress * 0.8); // 在图片宽度的10%-90%范围内
        const y = rowY;

        console.log(`Adding control point [${row},${col}] at (${x.toFixed(2)}, ${y.toFixed(2)})`);

        // 添加控制点到引擎
        const point = engine.addControlPoint({ x, y }, 100 + rowProgress * 50);

        if (point) {
          // 记录控制点信息用于动画
          swayControlPoints.push({
            point: point,
            originalX: x,
            originalY: y,
            amplitude: amplitude,
            frequency: frequency,
            phase: col * Math.PI * 0.3 // 相邻控制点有相位差，形成波浪效果
          });

          console.log(`Control point [${row},${col}] added with amplitude=${amplitude.toFixed(2)}, frequency=${frequency.toFixed(4)}`);
        } else {
          console.warn(`Failed to add control point [${row},${col}]`);
        }
      }
    }

    console.log(`Added ${swayControlPoints.length} control points for animation`);

    // 开始动画循环
    animateSwaying();
  }

  function animateSwaying() {
    if (!engine || swayControlPoints.length === 0) return;

    const time = Date.now() * 0.001; // 转换为秒
    let movedCount = 0;

    // 更新每个控制点的位置
    swayControlPoints.forEach(({ point, originalX, originalY, amplitude, frequency, phase }, index) => {
      // 主要的正弦波摆动
      const primaryWave = Math.sin(time * frequency + phase) * amplitude;

      // 添加一个较小的二次波，增加自然感
      const secondaryWave = Math.sin(time * frequency * 1.7 + phase * 0.7) * amplitude * 0.3;

      // 添加缓慢的整体摆动
      const globalSway = Math.sin(time * 0.5) * amplitude * 0.2;

      // 组合所有波动
      const totalOffset = primaryWave + secondaryWave + globalSway;
      const newX = originalX + totalOffset;

      // 获取当前位置
      const currentPos = point.getData().position;
      const oldX = currentPos.x;

      // 更新位置（只改变 X 坐标，保持 Y 坐标不变）
      point.setPosition({ x: newX, y: originalY });

      // 手动更新控制点的原始位置为真正的原始位置
      const pointData = point.getData();
      pointData.originalPosition = { x: originalX, y: originalY };

      // 检查是否真的移动了
      const newPos = point.getData().position;
      if (Math.abs(newPos.x - oldX) > 0.1) {
        movedCount++;
      }

      // 每隔一段时间打印第一个控制点的信息
      if (index === 0 && Math.floor(time) % 3 === 0 && time % 1 < 0.05) {
        console.log(`Fabric animation: primary=${primaryWave.toFixed(2)}, secondary=${secondaryWave.toFixed(2)}, global=${globalSway.toFixed(2)}, total=${totalOffset.toFixed(2)}`);
      }
    });

    // 手动触发变形计算
    if (movedCount > 0) {
      engine.applyDeformation();
    }

    // 继续动画
    animationId = requestAnimationFrame(() => animateSwaying());
  }

  function stopSwayAnimation() {
    if (animationId) {
      cancelAnimationFrame(animationId);
      animationId = null;
    }
    swayControlPoints = [];
    addLog('info', '摆动动画已停止');
  }

  function handleStopSway() {
    stopSwayAnimation();
  }

  function handleGridConfigChange(event: CustomEvent) {
    gridConfig = event.detail.config;
    if (engine) {
      engine.setGridConfig(gridConfig);
      addLog('info', '网格配置已更新');
    }
  }

  function handleControlPointConfigChange(event: CustomEvent) {
    controlPointConfig = event.detail.config;
    // 更新现有控制点的配置
    allControlPoints.forEach(point => {
      point.updateConfig(controlPointConfig);
    });
    addLog('info', '控制点配置已更新');
  }

  function handleEngineReady(event: CustomEvent) {
    engine = event.detail.engine;
    addLog('success', '引擎初始化完成');
  }

  function handlePointAdded(event: CustomEvent) {
    updateControlPoints();
  }

  function handlePointRemoved(event: CustomEvent) {
    updateControlPoints();
  }

  function handlePointSelected(event: CustomEvent) {
    updateControlPoints();
  }

  function handleDeleteSelected() {
    if (engine) {
      selectedControlPoints.forEach(point => {
        engine!.removeControlPoint(point.id);
      });
      updateControlPoints();
      addLog('info', `删除了 ${selectedControlPoints.length} 个控制点`);
    }
  }

  function handleClearAll() {
    if (engine) {
      engine.clearAllControlPoints();
      updateControlPoints();
      addLog('info', '清除了所有控制点');
    }
  }

  function handleSelectPoint(event: CustomEvent) {
    const { pointId } = event.detail;
    // 这里可以实现点击选择逻辑
    addLog('info', `选择控制点: ${pointId.slice(0, 6)}`);
  }

  function handleDeletePoint(event: CustomEvent) {
    const { pointId } = event.detail;
    if (engine) {
      engine.removeControlPoint(pointId);
      updateControlPoints();
      addLog('info', `删除控制点: ${pointId.slice(0, 6)}`);
    }
  }

  function handleInfluenceChange(event: CustomEvent) {
    const { pointId, influence } = event.detail;
    const point = allControlPoints.find(p => p.id === pointId);
    if (point) {
      point.setInfluenceRadius(influence);
      addLog('info', `更新控制点影响半径: ${influence}px`);
    }
  }

  function updateControlPoints() {
    if (engine) {
      allControlPoints = engine.getAllControlPoints();
      selectedControlPoints = engine.getSelectedPoints();
    }
  }

  function addLog(type: 'info' | 'success' | 'warning' | 'error', message: string) {
    if (bottomPanel) {
      bottomPanel.addLog(type, message);
    }
  }

  function getModeText(mode: InteractionMode): string {
    switch (mode) {
      case Mode.NONE: return '无交互';
      case Mode.ADD_POINT: return '添加控制点';
      case Mode.MOVE_POINT: return '移动控制点';
      case Mode.SELECT: return '选择控制点';
      default: return '未知模式';
    }
  }

  // 定期更新控制点状态
  onMount(() => {
    const interval = setInterval(() => {
      if (engine && hasImage) {
        updateControlPoints();
      }
    }, 100);

    return () => clearInterval(interval);
  });
</script>

<div class="app-container">
  <!-- 使用 PaneGroup 创建可拉伸的布局 -->
  <PaneGroup direction="vertical" class="pane-group-root">
    <!-- 顶部面板 -->
    <Pane defaultSize={15} minSize={10} maxSize={25} class="top-pane">
      <TopPanel
        {currentMode}
        {hasImage}
        on:mode-change={handleModeChange}
        on:load-image={handleLoadImage}
        on:reset={handleReset}
        on:export={handleExport}
        on:import={handleImport}
        on:auto-sway={handleAutoSway}
        on:stop-sway={handleStopSway}
      />
    </Pane>

    <PaneResizer class="pane-resizer horizontal" />

    <!-- 主内容区域 -->
    <Pane defaultSize={65} minSize={45} class="main-content-pane">
      <PaneGroup direction="horizontal" class="pane-group-main">
        <!-- 左侧面板 -->
        <Pane defaultSize={22} minSize={15} maxSize={35} class="left-pane">
          <LeftPanel
            {gridConfig}
            {controlPointConfig}
            {hasImage}
            on:grid-config-change={handleGridConfigChange}
            on:control-point-config-change={handleControlPointConfigChange}
          />
        </Pane>

        <PaneResizer class="pane-resizer vertical" />

        <!-- 中央画布区域 -->
        <Pane defaultSize={56} minSize={30} class="center-pane">
          <CenterPanel
            bind:this={centerPanel}
            {currentMode}
            on:engine-ready={handleEngineReady}
            on:image-loaded={() => hasImage = true}
            on:point-added={handlePointAdded}
            on:point-removed={handlePointRemoved}
            on:point-selected={handlePointSelected}
            on:log={(e) => addLog(e.detail.type, e.detail.message)}
            on:error={(e) => addLog('error', e.detail.message)}
          />
        </Pane>

        <PaneResizer class="pane-resizer vertical" />

        <!-- 右侧面板 -->
        <Pane defaultSize={22} minSize={15} maxSize={35} class="right-pane">
          <RightPanel
            selectedPoints={selectedControlPoints}
            allPoints={allControlPoints}
            {hasImage}
            on:delete-selected={handleDeleteSelected}
            on:clear-all={handleClearAll}
            on:select-point={handleSelectPoint}
            on:delete-point={handleDeletePoint}
            on:influence-change={handleInfluenceChange}
          />
        </Pane>
      </PaneGroup>
    </Pane>

    <PaneResizer class="pane-resizer horizontal" />

    <!-- 底部面板 -->
    <Pane defaultSize={20} minSize={15} maxSize={35} class="bottom-pane">
      <BottomPanel
        bind:this={bottomPanel}
        {currentMode}
        {hasImage}
        pointCount={allControlPoints.length}
        selectedCount={selectedControlPoints.length}
        {isProcessing}
      />
    </Pane>
  </PaneGroup>
</div>

<style>
  :global(*) {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  :global(body) {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f8fafc;
    color: #1e293b;
    overflow: hidden;
  }

  .app-container {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
  }

  /* PaneForge 样式 */
  :global(.pane-group-root) {
    height: 100%;
    width: 100%;
  }

  :global(.pane-group-main) {
    height: 100%;
    width: 100%;
  }

  /* 面板样式 */
  :global(.top-pane) {
    overflow: hidden;
    height: 100%;
    width: 100%;
  }

  :global(.left-pane) {
    overflow: hidden;
    height: 100%;
    width: 100%;
  }

  :global(.center-pane) {
    overflow: hidden;
    height: 100%;
    width: 100%;
  }

  :global(.right-pane) {
    overflow: hidden;
    height: 100%;
    width: 100%;
  }

  :global(.bottom-pane) {
    overflow: hidden;
    height: 100%;
    width: 100%;
  }

  :global(.main-content-pane) {
    overflow: hidden;
    height: 100%;
    width: 100%;
  }

  /* 确保面板组件填充整个容器 */
  :global(.top-pane > *) {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  :global(.left-pane > *) {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  :global(.center-pane > *) {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  :global(.right-pane > *) {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  :global(.bottom-pane > *) {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  /* 调试边框 - 可以临时启用来查看布局 */
  /*
  :global(.top-pane) {
    border: 2px solid red !important;
  }
  :global(.left-pane) {
    border: 2px solid blue !important;
  }
  :global(.center-pane) {
    border: 2px solid green !important;
  }
  :global(.right-pane) {
    border: 2px solid orange !important;
  }
  :global(.bottom-pane) {
    border: 2px solid purple !important;
  }
  */

  /* 拖拽分隔条样式 */
  :global(.pane-resizer) {
    background-color: #e2e8f0;
    transition: background-color 0.2s ease;
    position: relative;
    z-index: 10;
  }

  :global(.pane-resizer:hover) {
    background-color: #cbd5e1;
  }

  :global(.pane-resizer:active) {
    background-color: #94a3b8;
  }

  :global(.pane-resizer.horizontal) {
    height: 4px;
    cursor: row-resize;
    width: 100%;
  }

  :global(.pane-resizer.vertical) {
    width: 4px;
    cursor: col-resize;
    height: 100%;
  }

  /* 拖拽时的视觉反馈 */
  :global(.pane-resizer.horizontal::after) {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 2px;
    background-color: #64748b;
    border-radius: 1px;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  :global(.pane-resizer.vertical::after) {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 40px;
    background-color: #64748b;
    border-radius: 1px;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  :global(.pane-resizer:hover::after) {
    opacity: 1;
  }

  /* TopPanel 特殊样式调整 */
  :global(.top-pane .top-panel) {
    min-height: 60px;
    flex-wrap: wrap;
    align-items: flex-start;
    padding: 0.5rem 1rem;
    gap: 1rem;
  }

  :global(.top-pane .panel-section) {
    flex-shrink: 0;
  }

  :global(.top-pane .panel-section h3) {
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
  }

  :global(.top-pane .mode-buttons) {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  :global(.top-pane .mode-btn) {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  :global(.top-pane .btn) {
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
  }

  /* 响应式设计 */
  @media (max-width: 1024px) {
    :global(.left-pane),
    :global(.right-pane) {
      min-width: 200px;
    }

    :global(.top-pane .top-panel) {
      flex-direction: column;
      align-items: stretch;
      gap: 0.5rem;
      padding: 0.5rem;
    }
  }

  @media (max-width: 768px) {
    :global(.pane-resizer.horizontal) {
      height: 6px;
    }

    :global(.pane-resizer.vertical) {
      width: 6px;
    }

    :global(.left-pane),
    :global(.right-pane) {
      min-width: 150px;
    }

    :global(.top-pane .panel-section) {
      margin-bottom: 0.5rem;
    }
  }

  /* 滚动条全局样式 */
  :global(::-webkit-scrollbar) {
    width: 8px;
    height: 8px;
  }

  :global(::-webkit-scrollbar-track) {
    background: #f1f5f9;
    border-radius: 4px;
  }

  :global(::-webkit-scrollbar-thumb) {
    background: #cbd5e1;
    border-radius: 4px;
  }

  :global(::-webkit-scrollbar-thumb:hover) {
    background: #94a3b8;
  }

  /* 深色模式支持 */
  @media (prefers-color-scheme: dark) {
    :global(body) {
      background-color: #0f172a;
      color: #f1f5f9;
    }

    :global(::-webkit-scrollbar-track) {
      background: #1e293b;
    }

    :global(::-webkit-scrollbar-thumb) {
      background: #475569;
    }

    :global(::-webkit-scrollbar-thumb:hover) {
      background: #64748b;
    }
  }
</style>
