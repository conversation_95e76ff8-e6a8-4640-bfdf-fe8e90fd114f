["\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\menu\\autogenerated\\default.toml"]