/**
 * 交互管理器 - 处理用户交互逻辑
 */

import { Application, Container, Rectangle, FederatedPointerEvent } from 'pixi.js';
import type { Point2D, InteractionState, ControlPointConfig } from '../types';
import { InteractionMode } from '../types';
import { EventEmitter } from './EventEmitter';
import { ControlPoint } from './ControlPoint';
import { MathUtils } from '../utils/math';

interface InteractionEvents {
  'mode-changed': { mode: InteractionMode };
  'point-added': { point: ControlPoint; position: Point2D };
  'point-selected': { point: ControlPoint };
  'point-deselected': { point: ControlPoint };
  'selection-changed': { selectedPoints: ControlPoint[] };
  'background-clicked': { position: Point2D };
}

export class InteractionManager extends EventEmitter<InteractionEvents> {
  private app: Application;
  private container: Container;
  private controlPoints: Map<string, ControlPoint> = new Map();
  private state: InteractionState;
  private config: ControlPointConfig;

  constructor(app: Application, container: Container, config: ControlPointConfig) {
    super();

    this.app = app;
    this.container = container;
    this.config = config;

    this.state = {
      mode: InteractionMode.NONE,
      selectedPoints: [],
      dragStartPosition: null,
      isDragging: false
    };

    this.setupInteraction();
  }

  /**
   * 设置交互事件
   */
  private setupInteraction(): void {
    // 设置容器为可交互
    this.container.interactive = true;
    this.container.hitArea = new Rectangle(0, 0, this.app.canvas.width, this.app.canvas.height);

    // 监听容器事件
    this.container.on('pointerdown', this.onContainerPointerDown.bind(this));
    this.container.on('pointermove', this.onContainerPointerMove.bind(this));
    this.container.on('pointerup', this.onContainerPointerUp.bind(this));
  }

  /**
   * 容器指针按下事件
   */
  private onContainerPointerDown(event: FederatedPointerEvent): void {
    const position = this.getLocalPosition(event);
    this.state.dragStartPosition = position;

    // 检查是否点击了控制点
    const clickedPoint = this.getPointAtPosition(position);

    if (clickedPoint) {
      this.handlePointClick(clickedPoint, event);
    } else {
      this.handleBackgroundClick(position, event);
    }
  }

  /**
   * 容器指针移动事件
   */
  private onContainerPointerMove(event: FederatedPointerEvent): void {
    const position = this.getLocalPosition(event);

    if (this.state.dragStartPosition && !this.state.isDragging) {
      const distance = MathUtils.distance(this.state.dragStartPosition, position);
      if (distance > 5) { // 拖拽阈值
        this.state.isDragging = true;
      }
    }
  }

  /**
   * 容器指针抬起事件
   */
  private onContainerPointerUp(event: FederatedPointerEvent): void {
    this.state.dragStartPosition = null;
    this.state.isDragging = false;
  }

  /**
   * 处理控制点点击
   */
  private handlePointClick(point: ControlPoint, event: FederatedPointerEvent): void {
    event.stopPropagation();

    switch (this.state.mode) {
      case InteractionMode.SELECT:
        this.togglePointSelection(point);
        break;
      case InteractionMode.MOVE_POINT:
        this.selectPoint(point);
        break;
    }
  }

  /**
   * 处理背景点击
   */
  private handleBackgroundClick(position: Point2D, event: FederatedPointerEvent): void {
    switch (this.state.mode) {
      case InteractionMode.ADD_POINT:
        this.addControlPoint(position);
        break;
      case InteractionMode.SELECT:
        if (!event.shiftKey) {
          this.clearSelection();
        }
        break;
    }

    this.emit('background-clicked', { position });
  }

  /**
   * 添加控制点
   */
  public addControlPoint(position: Point2D, influenceRadius: number = 100): ControlPoint {
    const point = new ControlPoint(position, this.config, influenceRadius);

    // 监听控制点事件
    point.on('dragstart', this.onPointDragStart.bind(this));
    point.on('drag', this.onPointDrag.bind(this));
    point.on('dragend', this.onPointDragEnd.bind(this));

    this.controlPoints.set(point.id, point);
    this.container.addChild(point);

    this.emit('point-added', { point, position });

    return point;
  }

  /**
   * 移除控制点
   */
  public removeControlPoint(pointId: string): boolean {
    const point = this.controlPoints.get(pointId);
    if (!point) return false;

    // 从选择中移除
    this.deselectPoint(point);

    // 从容器中移除
    this.container.removeChild(point);

    // 销毁控制点
    point.destroy();

    // 从映射中移除
    this.controlPoints.delete(pointId);

    return true;
  }

  /**
   * 控制点拖拽开始
   */
  private onPointDragStart(data: { point: any; event: FederatedPointerEvent }): void {
    // 如果点击的点不在选择中，则选择它
    if (!this.state.selectedPoints.includes(data.point.id)) {
      this.selectPoint(this.controlPoints.get(data.point.id)!);
    }
  }

  /**
   * 控制点拖拽中
   */
  private onPointDrag(data: { point: any; event: FederatedPointerEvent }): void {
    // 触发实时变形更新
    this.emit('point-moved', { point: data.point });
  }

  /**
   * 控制点拖拽结束
   */
  private onPointDragEnd(data: { point: any; event: FederatedPointerEvent }): void {
    // 拖拽结束后也触发更新
    this.emit('point-moved', { point: data.point });
  }

  /**
   * 选择控制点
   */
  public selectPoint(point: ControlPoint): void {
    if (!this.state.selectedPoints.includes(point.id)) {
      this.state.selectedPoints.push(point.id);
      point.setSelected(true);
      this.emit('point-selected', { point });
      this.emit('selection-changed', { selectedPoints: this.getSelectedPoints() });
    }
  }

  /**
   * 取消选择控制点
   */
  public deselectPoint(point: ControlPoint): void {
    const index = this.state.selectedPoints.indexOf(point.id);
    if (index !== -1) {
      this.state.selectedPoints.splice(index, 1);
      point.setSelected(false);
      this.emit('point-deselected', { point });
      this.emit('selection-changed', { selectedPoints: this.getSelectedPoints() });
    }
  }

  /**
   * 切换控制点选择状态
   */
  public togglePointSelection(point: ControlPoint): void {
    if (this.state.selectedPoints.includes(point.id)) {
      this.deselectPoint(point);
    } else {
      this.selectPoint(point);
    }
  }

  /**
   * 清除所有选择
   */
  public clearSelection(): void {
    const selectedPoints = this.getSelectedPoints();
    selectedPoints.forEach(point => {
      point.setSelected(false);
    });
    this.state.selectedPoints = [];
    this.emit('selection-changed', { selectedPoints: [] });
  }

  /**
   * 设置交互模式
   */
  public setMode(mode: InteractionMode): void {
    if (this.state.mode !== mode) {
      this.state.mode = mode;
      this.emit('mode-changed', { mode });

      // 根据模式更新光标
      this.updateCursor();
    }
  }

  /**
   * 更新光标样式
   */
  private updateCursor(): void {
    switch (this.state.mode) {
      case InteractionMode.ADD_POINT:
        this.container.cursor = 'crosshair';
        break;
      case InteractionMode.MOVE_POINT:
        this.container.cursor = 'move';
        break;
      case InteractionMode.SELECT:
        this.container.cursor = 'default';
        break;
      default:
        this.container.cursor = 'default';
    }
  }

  /**
   * 获取指定位置的控制点
   */
  private getPointAtPosition(position: Point2D): ControlPoint | null {
    for (const point of this.controlPoints.values()) {
      if (point.distanceTo(position) <= this.config.radius) {
        return point;
      }
    }
    return null;
  }

  /**
   * 获取本地坐标
   */
  private getLocalPosition(event: PIXI.FederatedPointerEvent): Point2D {
    const localPos = this.container.toLocal(event.global);
    return { x: localPos.x, y: localPos.y };
  }

  /**
   * 获取当前选中的控制点
   */
  public getSelectedPoints(): ControlPoint[] {
    return this.state.selectedPoints
      .map(id => this.controlPoints.get(id))
      .filter(point => point !== undefined) as ControlPoint[];
  }

  /**
   * 获取所有控制点
   */
  public getAllPoints(): ControlPoint[] {
    return Array.from(this.controlPoints.values());
  }

  /**
   * 获取当前交互模式
   */
  public getMode(): InteractionMode {
    return this.state.mode;
  }

  /**
   * 销毁交互管理器
   */
  public destroy(): void {
    // 移除所有控制点
    this.controlPoints.forEach(point => {
      this.container.removeChild(point);
      point.destroy();
    });
    this.controlPoints.clear();

    // 移除事件监听
    this.container.removeAllListeners();
    this.removeAllListeners();
  }
}
