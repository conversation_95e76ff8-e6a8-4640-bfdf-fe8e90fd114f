["\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\tray\\autogenerated\\commands\\get_by_id.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\tray\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\tray\\autogenerated\\commands\\remove_by_id.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\tray\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\tray\\autogenerated\\commands\\set_icon_as_template.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\tray\\autogenerated\\commands\\set_menu.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\tray\\autogenerated\\commands\\set_show_menu_on_left_click.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\tray\\autogenerated\\commands\\set_temp_dir_path.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\tray\\autogenerated\\commands\\set_title.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\tray\\autogenerated\\commands\\set_tooltip.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\tray\\autogenerated\\commands\\set_visible.toml", "\\\\?\\D:\\project\\eve\\eve\\src-tauri\\target\\debug\\build\\tauri-0e82150e90e008a3\\out\\permissions\\tray\\autogenerated\\default.toml"]