/**
 * 网格变形引擎主类
 */

import { Application, Container, Assets, Texture } from 'pixi.js';
import type {
  MeshEngineConfig,
  MeshEngineEvents,
  Point2D,
  DeformationOptions
} from './types';
import { InteractionMode } from './types';
import { EventEmitter } from './core/EventEmitter';
import { GridMesh } from './core/GridMesh';
import { InteractionManager } from './core/InteractionManager';
import { ControlPoint } from './core/ControlPoint';

export class MeshEngine extends EventEmitter<MeshEngineEvents> {
  private app: Application | null = null;
  private config: MeshEngineConfig;
  private gridMesh: GridMesh | null = null;
  private interactionManager: InteractionManager | null = null;
  private mainContainer: Container | null = null;
  private isInitialized: boolean = false;

  constructor(config: MeshEngineConfig) {
    super();
    this.config = config;
  }

  /**
   * 初始化引擎
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.warn('MeshEngine is already initialized');
      return;
    }

    try {
      console.log('Starting MeshEngine initialization...');
      console.log('Config:', this.config);

      // 创建PIXI应用
      this.app = new Application();
      console.log('PIXI Application created');

      // 初始化PIXI应用
      await this.app.init({
        width: this.config.width,
        height: this.config.height,
        backgroundColor: this.config.backgroundColor,
        antialias: true,
        resolution: window.devicePixelRatio || 1,
        autoDensity: true
      });
      console.log('PIXI Application initialized');

      // 创建主容器
      this.mainContainer = new Container();
      console.log('Main container created');

      // 将canvas添加到DOM
      this.config.container.appendChild(this.app.canvas);
      console.log('Canvas added to DOM');

      // 添加主容器到舞台
      this.app.stage.addChild(this.mainContainer);
      console.log('Main container added to stage');

      // 创建交互管理器
      this.interactionManager = new InteractionManager(
        this.app,
        this.mainContainer,
        this.config.controlPoint
      );
      console.log('Interaction manager created');

      // 设置事件监听器
      this.setupEventListeners();
      console.log('Event listeners setup');

      this.isInitialized = true;

      console.log('MeshEngine initialized successfully');
    } catch (error) {
      console.error('Failed to initialize MeshEngine:', error);
      throw error;
    }
  }

  /**
   * 加载图片并创建网格
   */
  public async loadImage(imageUrl: string): Promise<void> {
    if (!this.isInitialized || !this.app || !this.mainContainer) {
      throw new Error('MeshEngine must be initialized before loading images');
    }

    try {
      console.log('Loading image from URL:', imageUrl);

      // 创建一个 Image 元素来预加载图片
      const img = new Image();
      img.crossOrigin = 'anonymous';

      // 等待图片加载完成
      const imageLoadPromise = new Promise<HTMLImageElement>((resolve, reject) => {
        img.onload = () => {
          console.log('Image loaded successfully:', img.width, 'x', img.height);
          resolve(img);
        };
        img.onerror = (error) => {
          console.error('Image load error:', error);
          reject(new Error('Failed to load image'));
        };
        img.src = imageUrl;
      });

      const loadedImage = await imageLoadPromise;

      // 使用加载完成的图片创建纹理
      const texture = Texture.from(loadedImage);

      console.log('Texture created from image:', texture);

      if (!texture) {
        throw new Error('Failed to create texture from image');
      }

      // 获取纹理尺寸 - 直接从加载的图片获取
      const textureWidth = loadedImage.width;
      const textureHeight = loadedImage.height;

      console.log('Image dimensions:', { width: textureWidth, height: textureHeight });

      if (textureWidth === 0 || textureHeight === 0) {
        throw new Error('Invalid image dimensions');
      }

      // 计算图片尺寸（保持宽高比）
      const imageSize = this.calculateImageSize(textureWidth, textureHeight);

      // 移除旧的网格
      if (this.gridMesh) {
        this.mainContainer.removeChild(this.gridMesh);
        this.gridMesh.destroy();
      }

      // 创建新的网格
      this.gridMesh = new GridMesh(texture, this.config.grid, imageSize);
      this.mainContainer.addChildAt(this.gridMesh, 0); // 添加到底层

      // 居中显示
      this.centerMesh();

      console.log('Image loaded and mesh created successfully');
    } catch (error) {
      console.error('Failed to load image:', error);
      throw error;
    }
  }

  /**
   * 计算图片显示尺寸
   */
  private calculateImageSize(originalWidth: number, originalHeight: number): { width: number; height: number } {
    const maxWidth = this.config.width * 0.8; // 留出边距
    const maxHeight = this.config.height * 0.8;

    const scaleX = maxWidth / originalWidth;
    const scaleY = maxHeight / originalHeight;
    const scale = Math.min(scaleX, scaleY);

    return {
      width: originalWidth * scale,
      height: originalHeight * scale
    };
  }

  /**
   * 居中网格
   */
  private centerMesh(): void {
    if (!this.gridMesh) return;

    const bounds = this.gridMesh.getBounds();
    this.gridMesh.x = (this.config.width - bounds.width) / 2;
    this.gridMesh.y = (this.config.height - bounds.height) / 2;
  }

  /**
   * 设置交互模式
   */
  public setInteractionMode(mode: InteractionMode): void {
    if (this.interactionManager) {
      this.interactionManager.setMode(mode);
      this.emit('interaction-mode-changed', { mode });
    }
  }

  /**
   * 添加控制点
   */
  public addControlPoint(position: Point2D, influenceRadius: number = 100): ControlPoint | null {
    if (this.interactionManager) {
      return this.interactionManager.addControlPoint(position, influenceRadius);
    }
    return null;
  }

  /**
   * 移除控制点
   */
  public removeControlPoint(pointId: string): boolean {
    if (this.interactionManager) {
      return this.interactionManager.removeControlPoint(pointId);
    }
    return false;
  }

  /**
   * 应用变形
   */
  public applyDeformation(options?: DeformationOptions): void {
    if (!this.gridMesh || !this.interactionManager) {
      console.warn('No mesh or interaction manager available for deformation');
      return;
    }

    const controlPoints = this.interactionManager.getAllPoints();
    console.log('Applying deformation with', controlPoints.length, 'control points');

    const deformationData = controlPoints.map(point => {
      // 将控制点坐标从画布坐标系转换到网格坐标系
      const meshBounds = this.gridMesh!.getBounds();
      const meshX = this.gridMesh!.x;
      const meshY = this.gridMesh!.y;

      // 转换坐标：从画布坐标转换为网格内部坐标
      const originalLocal = {
        x: point.data.originalPosition.x - meshX,
        y: point.data.originalPosition.y - meshY
      };

      const currentLocal = {
        x: point.data.position.x - meshX,
        y: point.data.position.y - meshY
      };

      const data = {
        original: originalLocal,
        current: currentLocal,
        influence: point.data.influenceRadius
      };

      const displacement = {
        x: data.current.x - data.original.x,
        y: data.current.y - data.original.y
      };

      console.log('Control point data (converted to mesh coordinates):', {
        id: point.id,
        originalGlobal: point.data.originalPosition,
        currentGlobal: point.data.position,
        originalLocal: data.original,
        currentLocal: data.current,
        influence: data.influence,
        displacement: displacement,
        displacementMagnitude: Math.sqrt(displacement.x * displacement.x + displacement.y * displacement.y)
      });
      return data;
    });

    if (deformationData.length > 0) {
      this.gridMesh.applyDeformation(deformationData);

      this.emit('mesh-updated', {
        meshData: this.gridMesh.getMeshData()
      });

      console.log('Deformation applied successfully');
    } else {
      console.log('No control points to apply deformation');
    }
  }

  /**
   * 重置网格
   */
  public resetMesh(): void {
    if (this.gridMesh) {
      this.gridMesh.reset();
    }

    // 重置所有控制点
    if (this.interactionManager) {
      this.interactionManager.getAllPoints().forEach(point => {
        point.reset();
      });
    }

    this.emit('mesh-updated', {
      meshData: this.gridMesh?.getMeshData() || {} as any
    });
  }

  /**
   * 设置网格配置
   */
  public setGridConfig(config: Partial<typeof this.config.grid>): void {
    this.config.grid = { ...this.config.grid, ...config };

    if (this.gridMesh) {
      this.gridMesh.setConfig(this.config.grid);
    }
  }

  /**
   * 获取当前选中的控制点
   */
  public getSelectedPoints(): ControlPoint[] {
    return this.interactionManager ? this.interactionManager.getSelectedPoints() : [];
  }

  /**
   * 获取所有控制点
   */
  public getAllControlPoints(): ControlPoint[] {
    return this.interactionManager ? this.interactionManager.getAllPoints() : [];
  }

  /**
   * 清除所有控制点
   */
  public clearAllControlPoints(): void {
    if (this.interactionManager) {
      const points = this.interactionManager.getAllPoints();
      points.forEach(point => {
        this.interactionManager!.removeControlPoint(point.id);
      });
    }
  }

  /**
   * 导出网格数据
   */
  public exportMeshData(): any {
    if (!this.gridMesh || !this.interactionManager) return null;

    return {
      meshData: this.gridMesh.getMeshData(),
      controlPoints: this.interactionManager.getAllPoints().map(point => point.getData()),
      config: this.config
    };
  }

  /**
   * 导入网格数据
   */
  public importMeshData(data: any): void {
    // 清除现有控制点
    this.clearAllControlPoints();

    // 重新创建控制点
    if (data.controlPoints) {
      data.controlPoints.forEach((pointData: any) => {
        const point = this.addControlPoint(pointData.position, pointData.influenceRadius);
        if (point) {
          point.setPosition(pointData.position);
        }
      });
    }

    // 应用变形
    this.applyDeformation();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.interactionManager) return;

    // 监听交互管理器事件
    this.interactionManager.on('point-added', (data) => {
      this.emit('point-added', { point: data.point.getData() });
      // 自动应用变形
      this.applyDeformation();
    });

    this.interactionManager.on('point-selected', (data) => {
      this.emit('point-selected', { point: data.point.getData() });
    });

    this.interactionManager.on('point-moved', (data) => {
      console.log('Point moved, applying deformation...');
      this.emit('point-moved', { point: data.point.getData() });
      // 实时应用变形
      this.applyDeformation();
    });

    this.interactionManager.on('selection-changed', () => {
      // 当选择改变时应用变形
      this.applyDeformation();
    });

    this.interactionManager.on('mode-changed', (data) => {
      this.emit('interaction-mode-changed', { mode: data.mode });
    });
  }

  /**
   * 调整画布大小
   */
  public resize(width: number, height: number): void {
    this.config.width = width;
    this.config.height = height;

    if (this.app) {
      this.app.renderer.resize(width, height);

      // 重新居中网格
      this.centerMesh();
    }
  }

  /**
   * 销毁引擎
   */
  public destroy(): void {
    // 销毁交互管理器
    if (this.interactionManager) {
      this.interactionManager.destroy();
    }

    // 销毁网格
    if (this.gridMesh) {
      this.gridMesh.destroy();
    }

    // 销毁PIXI应用
    if (this.app) {
      this.app.destroy(true, { children: true, texture: true });
    }

    // 移除事件监听器
    this.removeAllListeners();

    this.isInitialized = false;

    console.log('MeshEngine destroyed');
  }

  /**
   * 获取当前交互模式
   */
  public getInteractionMode(): InteractionMode {
    return this.interactionManager ? this.interactionManager.getMode() : InteractionMode.NONE;
  }

  /**
   * 获取画布尺寸
   */
  public getCanvasSize(): { width: number; height: number } {
    if (this.app && this.app.canvas) {
      return {
        width: this.app.canvas.width,
        height: this.app.canvas.height
      };
    }
    return { width: this.config.width, height: this.config.height };
  }

  /**
   * 获取网格边界信息
   */
  public getMeshBounds(): { x: number; y: number; width: number; height: number } | null {
    if (!this.gridMesh) {
      return null;
    }

    const bounds = this.gridMesh.getBounds();
    return {
      x: bounds.x,
      y: bounds.y,
      width: bounds.width,
      height: bounds.height
    };
  }

  /**
   * 检查引擎是否已初始化
   */
  public isReady(): boolean {
    return this.isInitialized;
  }
}
