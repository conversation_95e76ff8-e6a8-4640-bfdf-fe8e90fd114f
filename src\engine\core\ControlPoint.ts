/**
 * 控制点类 - 管理可拖拽的控制点
 */

import * as PIXI from 'pixi.js';
import type { Point2D, ControlPointData, ControlPointConfig } from '../types';
import { MathUtils } from '../utils/math';

export class ControlPoint extends PIXI.Container {
  public readonly id: string;
  public data: ControlPointData;
  private config: ControlPointConfig;
  private circle: PIXI.Graphics;
  private outline: PIXI.Graphics;
  private isDragging: boolean = false;
  private dragOffset: Point2D = { x: 0, y: 0 };

  constructor(position: Point2D, config: ControlPointConfig, influenceRadius: number = 100) {
    super();

    this.id = MathUtils.generateId();
    this.config = config;
    
    this.data = {
      id: this.id,
      position: { ...position },
      originalPosition: { ...position },
      isSelected: false,
      isDragging: false,
      influenceRadius
    };

    this.interactive = true;
    this.cursor = 'pointer';

    this.createVisuals();
    this.setupInteraction();
    this.updatePosition();
  }

  /**
   * 创建视觉元素
   */
  private createVisuals(): void {
    // 外圈（描边）
    this.outline = new PIXI.Graphics();
    this.addChild(this.outline);

    // 内圈（填充）
    this.circle = new PIXI.Graphics();
    this.addChild(this.circle);

    this.updateVisuals();
  }

  /**
   * 更新视觉样式
   */
  private updateVisuals(): void {
    const { radius, color, strokeWidth, strokeColor, hoverColor, selectedColor } = this.config;
    
    // 清除之前的绘制
    this.outline.clear();
    this.circle.clear();

    // 确定颜色
    let fillColor = color;
    if (this.data.isSelected) {
      fillColor = selectedColor;
    } else if (this.isMouseOver) {
      fillColor = hoverColor;
    }

    // 绘制外圈（描边）
    this.outline
      .lineStyle(strokeWidth, strokeColor, 1)
      .drawCircle(0, 0, radius + strokeWidth / 2);

    // 绘制内圈（填充）
    this.circle
      .beginFill(fillColor, 1)
      .drawCircle(0, 0, radius)
      .endFill();
  }

  /**
   * 设置交互事件
   */
  private setupInteraction(): void {
    this.on('pointerdown', this.onPointerDown.bind(this));
    this.on('pointermove', this.onPointerMove.bind(this));
    this.on('pointerup', this.onPointerUp.bind(this));
    this.on('pointerupoutside', this.onPointerUp.bind(this));
    this.on('pointerover', this.onPointerOver.bind(this));
    this.on('pointerout', this.onPointerOut.bind(this));
  }

  private isMouseOver: boolean = false;

  private onPointerDown(event: PIXI.FederatedPointerEvent): void {
    this.isDragging = true;
    this.data.isDragging = true;
    
    const globalPos = event.global;
    this.dragOffset = {
      x: globalPos.x - this.data.position.x,
      y: globalPos.y - this.data.position.y
    };

    // 阻止事件冒泡
    event.stopPropagation();
    
    this.emit('dragstart', { point: this.data, event });
  }

  private onPointerMove(event: PIXI.FederatedPointerEvent): void {
    if (this.isDragging) {
      const globalPos = event.global;
      const newPosition = {
        x: globalPos.x - this.dragOffset.x,
        y: globalPos.y - this.dragOffset.y
      };

      this.setPosition(newPosition);
      this.emit('drag', { point: this.data, event });
    }
  }

  private onPointerUp(event: PIXI.FederatedPointerEvent): void {
    if (this.isDragging) {
      this.isDragging = false;
      this.data.isDragging = false;
      this.emit('dragend', { point: this.data, event });
    }
  }

  private onPointerOver(): void {
    this.isMouseOver = true;
    this.updateVisuals();
    this.emit('hover', { point: this.data });
  }

  private onPointerOut(): void {
    this.isMouseOver = false;
    this.updateVisuals();
    this.emit('unhover', { point: this.data });
  }

  /**
   * 设置位置
   */
  public setPosition(position: Point2D): void {
    this.data.position = { ...position };
    this.updatePosition();
  }

  /**
   * 更新显示位置
   */
  private updatePosition(): void {
    this.x = this.data.position.x;
    this.y = this.data.position.y;
  }

  /**
   * 设置选中状态
   */
  public setSelected(selected: boolean): void {
    this.data.isSelected = selected;
    this.updateVisuals();
  }

  /**
   * 设置影响半径
   */
  public setInfluenceRadius(radius: number): void {
    this.data.influenceRadius = radius;
  }

  /**
   * 获取与另一个点的距离
   */
  public distanceTo(point: Point2D): number {
    return MathUtils.distance(this.data.position, point);
  }

  /**
   * 检查点是否在影响范围内
   */
  public isInInfluenceRange(point: Point2D): boolean {
    return this.distanceTo(point) <= this.data.influenceRadius;
  }

  /**
   * 重置到原始位置
   */
  public reset(): void {
    this.setPosition(this.data.originalPosition);
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<ControlPointConfig>): void {
    this.config = { ...this.config, ...config };
    this.updateVisuals();
  }

  /**
   * 销毁控制点
   */
  public destroy(): void {
    this.removeAllListeners();
    super.destroy();
  }

  /**
   * 获取控制点数据的副本
   */
  public getData(): ControlPointData {
    return {
      ...this.data,
      position: { ...this.data.position },
      originalPosition: { ...this.data.originalPosition }
    };
  }
}
