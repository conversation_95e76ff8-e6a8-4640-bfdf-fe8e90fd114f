/**
 * 核心类型定义
 */

export interface Point2D {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface Bounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface GridConfig {
  rows: number;
  cols: number;
  showGrid: boolean;
  gridColor: number;
  gridAlpha: number;
}

export interface ControlPointConfig {
  radius: number;
  color: number;
  hoverColor: number;
  selectedColor: number;
  strokeWidth: number;
  strokeColor: number;
}

export interface MeshEngineConfig {
  container: HTMLElement;
  width: number;
  height: number;
  backgroundColor: number;
  grid: GridConfig;
  controlPoint: ControlPointConfig;
  enableInteraction: boolean;
}

export interface ControlPointData {
  id: string;
  position: Point2D;
  originalPosition: Point2D;
  isSelected: boolean;
  isDragging: boolean;
  influenceRadius: number;
}

export interface MeshVertex {
  position: Point2D;
  uv: Point2D;
  originalPosition: Point2D;
}

export interface Triangle {
  vertices: [number, number, number]; // 顶点索引
}

export interface GridMeshData {
  vertices: MeshVertex[];
  triangles: Triangle[];
  rows: number;
  cols: number;
}

export enum InteractionMode {
  NONE = 'none',
  ADD_POINT = 'add_point',
  MOVE_POINT = 'move_point',
  SELECT = 'select'
}

export interface InteractionState {
  mode: InteractionMode;
  selectedPoints: string[];
  dragStartPosition: Point2D | null;
  isDragging: boolean;
}

export interface DeformationOptions {
  algorithm: 'linear' | 'rbf' | 'biharmonic';
  smoothness: number;
  preserveEdges: boolean;
}

// 事件类型
export interface MeshEngineEvents {
  'point-added': { point: ControlPointData };
  'point-removed': { pointId: string };
  'point-moved': { point: ControlPointData };
  'point-selected': { point: ControlPointData };
  'mesh-updated': { meshData: GridMeshData };
  'interaction-mode-changed': { mode: InteractionMode };
}

export type EventCallback<T = any> = (data: T) => void;
