<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { InteractionMode } from '../engine';
  import { InteractionMode as Mode } from '../engine';

  const dispatch = createEventDispatcher();

  export let currentMode: InteractionMode = Mode.NONE;
  export let hasImage: boolean = false;

  let fileInput: HTMLInputElement;

  function handleModeChange(mode: InteractionMode) {
    currentMode = mode;
    dispatch('mode-change', { mode });
  }

  function handleFileSelect() {
    fileInput.click();
  }

  function handleFileChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];
    if (file) {
      console.log('File selected:', file);
      console.log('File type:', file.type);
      console.log('File size:', file.size);

      const url = URL.createObjectURL(file);
      console.log('Created blob URL:', url);

      dispatch('load-image', { url, file });
    }
  }

  function handleReset() {
    dispatch('reset');
  }

  function handleExport() {
    dispatch('export');
  }

  function handleImport() {
    dispatch('import');
  }
</script>

<div class="top-panel">
  <div class="panel-section">
    <h3>文件操作</h3>
    <div class="button-group">
      <button class="btn btn-primary" on:click={handleFileSelect}>
        <span class="icon">📁</span>
        加载图片
      </button>
      <input
        bind:this={fileInput}
        type="file"
        accept="image/*"
        style="display: none"
        on:change={handleFileChange}
      />

      {#if hasImage}
        <button class="btn btn-secondary" on:click={handleExport}>
          <span class="icon">💾</span>
          导出
        </button>
        <button class="btn btn-secondary" on:click={handleImport}>
          <span class="icon">📥</span>
          导入
        </button>
      {/if}
    </div>
  </div>

  <div class="panel-section">
    <h3>交互模式</h3>
    <div class="mode-buttons">
      <button
        class="mode-btn {currentMode === Mode.NONE ? 'active' : ''}"
        on:click={() => handleModeChange(Mode.NONE)}
        title="无交互"
      >
        <span class="icon">🚫</span>
        无交互
      </button>

      <button
        class="mode-btn {currentMode === Mode.ADD_POINT ? 'active' : ''}"
        on:click={() => handleModeChange(Mode.ADD_POINT)}
        title="添加控制点"
        disabled={!hasImage}
      >
        <span class="icon">➕</span>
        添加点
      </button>

      <button
        class="mode-btn {currentMode === Mode.MOVE_POINT ? 'active' : ''}"
        on:click={() => handleModeChange(Mode.MOVE_POINT)}
        title="移动控制点"
        disabled={!hasImage}
      >
        <span class="icon">✋</span>
        移动点
      </button>

      <button
        class="mode-btn {currentMode === Mode.SELECT ? 'active' : ''}"
        on:click={() => handleModeChange(Mode.SELECT)}
        title="选择控制点"
        disabled={!hasImage}
      >
        <span class="icon">🎯</span>
        选择
      </button>
    </div>
  </div>

  {#if hasImage}
    <div class="panel-section">
      <h3>操作</h3>
      <div class="button-group">
        <button class="btn btn-warning" on:click={handleReset}>
          <span class="icon">🔄</span>
          重置
        </button>
      </div>
    </div>
  {/if}
</div>

<style>
  .top-panel {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .panel-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .panel-section h3 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    opacity: 0.9;
  }

  .button-group {
    display: flex;
    gap: 0.5rem;
  }

  .mode-buttons {
    display: flex;
    gap: 0.25rem;
  }

  .btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    backdrop-filter: blur(10px);
  }

  .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }

  .btn:active {
    transform: translateY(0);
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  .btn-primary {
    background: rgba(34, 197, 94, 0.8);
  }

  .btn-primary:hover {
    background: rgba(34, 197, 94, 0.9);
  }

  .btn-secondary {
    background: rgba(59, 130, 246, 0.8);
  }

  .btn-secondary:hover {
    background: rgba(59, 130, 246, 0.9);
  }

  .btn-warning {
    background: rgba(245, 158, 11, 0.8);
  }

  .btn-warning:hover {
    background: rgba(245, 158, 11, 0.9);
  }

  .mode-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .mode-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .mode-btn.active {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
  }

  .mode-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }

  .icon {
    font-size: 1rem;
  }

  @media (max-width: 768px) {
    .top-panel {
      flex-direction: column;
      gap: 1rem;
      padding: 0.75rem;
    }

    .panel-section {
      width: 100%;
    }

    .button-group,
    .mode-buttons {
      flex-wrap: wrap;
    }
  }
</style>
