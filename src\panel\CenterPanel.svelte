<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { createEventDispatcher } from 'svelte';
  import { createMeshEngine, InteractionMode, type MeshEngine } from '../engine';

  const dispatch = createEventDispatcher();

  export let currentMode: InteractionMode = InteractionMode.NONE;

  let canvasContainer: HTMLDivElement;
  let engine: MeshEngine | null = null;
  let isInitialized = false;
  let isLoading = false;
  let error: string | null = null;

  onMount(async () => {
    try {
      // 等待一小段时间确保DOM已经渲染
      await new Promise(resolve => setTimeout(resolve, 100));
      await initializeEngine();
    } catch (err) {
      console.error('Failed to initialize engine:', err);
      error = err instanceof Error ? err.message : '初始化失败';
      dispatch('error', { message: error });
    }
  });

  onDestroy(() => {
    if (engine) {
      engine.destroy();
      engine = null;
    }
  });

  async function initializeEngine() {
    if (!canvasContainer || isInitialized) return;

    isLoading = true;
    error = null;

    try {
      // 确保容器有正确的尺寸
      const containerWidth = canvasContainer.clientWidth || 800;
      const containerHeight = canvasContainer.clientHeight || 600;

      console.log('Container dimensions:', { width: containerWidth, height: containerHeight });

      // 创建引擎实例
      engine = createMeshEngine(canvasContainer, {
        width: containerWidth,
        height: containerHeight,
        backgroundColor: 0x2a2a2a,
        grid: {
          rows: 10,
          cols: 10,
          showGrid: true,
          gridColor: 0x00ff00,
          gridAlpha: 0.3
        },
        controlPoint: {
          radius: 8,
          color: 0xff4444,
          hoverColor: 0xff8888,
          selectedColor: 0xffff44,
          strokeWidth: 2,
          strokeColor: 0x000000
        }
      });

      // 初始化引擎
      if (engine) {
        await engine.initialize();

        // 设置事件监听器
        setupEngineEvents();

        isInitialized = true;
        dispatch('engine-ready', { engine });
        dispatch('log', { type: 'success', message: '引擎初始化成功' });
      } else {
        throw new Error('Failed to create engine instance');
      }

    } catch (err) {
      console.error('Engine initialization failed:', err);
      error = err instanceof Error ? err.message : '初始化失败';
      dispatch('error', { message: error });
      dispatch('log', { type: 'error', message: `引擎初始化失败: ${error}` });
    } finally {
      isLoading = false;
    }
  }

  function setupEngineEvents() {
    if (!engine) return;

    // 监听控制点添加
    engine.on('point-added', (data) => {
      dispatch('point-added', data);
      dispatch('log', { type: 'info', message: `添加控制点: (${Math.round(data.point.position.x)}, ${Math.round(data.point.position.y)})` });
    });

    // 监听控制点移除
    engine.on('point-removed', (data) => {
      dispatch('point-removed', data);
      dispatch('log', { type: 'info', message: `移除控制点: ${data.pointId}` });
    });

    // 监听控制点移动
    engine.on('point-moved', (data) => {
      dispatch('point-moved', data);
    });

    // 监听控制点选择
    engine.on('point-selected', (data) => {
      dispatch('point-selected', data);
      dispatch('log', { type: 'info', message: `选中控制点: ${data.point.id.slice(0, 6)}` });
    });

    // 监听网格更新
    engine.on('mesh-updated', (data) => {
      dispatch('mesh-updated', data);
    });

    // 监听交互模式变化
    engine.on('interaction-mode-changed', (data) => {
      dispatch('interaction-mode-changed', data);
      dispatch('log', { type: 'info', message: `切换交互模式: ${getModeText(data.mode)}` });
    });
  }

  function getModeText(mode: InteractionMode): string {
    switch (mode) {
      case InteractionMode.NONE: return '无交互';
      case InteractionMode.ADD_POINT: return '添加控制点';
      case InteractionMode.MOVE_POINT: return '移动控制点';
      case InteractionMode.SELECT: return '选择控制点';
      default: return '未知模式';
    }
  }

  // 响应外部模式变化
  $: if (engine && isInitialized) {
    engine.setInteractionMode(currentMode);
  }

  // 导出引擎实例供父组件使用
  export function getEngine(): MeshEngine | null {
    return engine;
  }

  // 加载图片
  export async function loadImage(url: string): Promise<void> {
    if (!engine || !isInitialized) {
      throw new Error('引擎未初始化');
    }

    isLoading = true;
    error = null;

    try {
      await engine.loadImage(url);
      dispatch('image-loaded', { url });
      dispatch('log', { type: 'success', message: '图片加载成功' });
    } catch (err) {
      console.error('Failed to load image:', err);
      error = err instanceof Error ? err.message : '图片加载失败';
      dispatch('error', { message: error });
      dispatch('log', { type: 'error', message: `图片加载失败: ${error}` });
      throw err;
    } finally {
      isLoading = false;
    }
  }

  // 重置网格
  export function resetMesh(): void {
    if (engine && isInitialized) {
      engine.resetMesh();
      dispatch('log', { type: 'info', message: '网格已重置' });
    }
  }

  // 导出数据
  export function exportData(): any {
    if (engine && isInitialized) {
      const data = engine.exportMeshData();
      dispatch('log', { type: 'success', message: '数据导出成功' });
      return data;
    }
    return null;
  }

  // 导入数据
  export function importData(data: any): void {
    if (engine && isInitialized) {
      engine.importMeshData(data);
      dispatch('log', { type: 'success', message: '数据导入成功' });
    }
  }

  // 处理窗口大小变化
  function handleResize() {
    if (engine && isInitialized && canvasContainer) {
      const rect = canvasContainer.getBoundingClientRect();
      engine.resize(rect.width, rect.height);
    }
  }

  // 监听窗口大小变化
  onMount(() => {
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  });
</script>

<div class="center-panel">
  <div class="canvas-container" bind:this={canvasContainer}>
    {#if isLoading}
      <div class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">
          {isInitialized ? '加载图片中...' : '初始化引擎中...'}
        </div>
      </div>
    {/if}

    {#if error}
      <div class="error-overlay">
        <div class="error-icon">❌</div>
        <div class="error-message">{error}</div>
        <button class="retry-btn" on:click={initializeEngine}>
          重试
        </button>
      </div>
    {/if}

    {#if !isInitialized && !isLoading && !error}
      <div class="welcome-overlay">
        <div class="welcome-icon">🎨</div>
        <h2>网格变形引擎</h2>
        <p>基于 PIXI.js 的图片网格变形工具</p>
        <div class="features">
          <div class="feature">
            <span class="feature-icon">📍</span>
            <span>添加控制点</span>
          </div>
          <div class="feature">
            <span class="feature-icon">🖱️</span>
            <span>拖拽变形</span>
          </div>
          <div class="feature">
            <span class="feature-icon">🎯</span>
            <span>精确控制</span>
          </div>
        </div>
      </div>
    {/if}

    {#if isInitialized && !isLoading && !error}
      <div class="canvas-overlay">
        <div class="mode-indicator">
          <span class="mode-text">{getModeText(currentMode)}</span>
        </div>
      </div>
    {/if}
  </div>
</div>

<style>
  .center-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #1a1a1a;
    position: relative;
    overflow: hidden;
  }

  .canvas-container {
    flex: 1;
    position: relative;
    background: #2a2a2a;
    overflow: hidden;
  }

  .loading-overlay,
  .error-overlay,
  .welcome-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(42, 42, 42, 0.95);
    backdrop-filter: blur(5px);
    z-index: 10;
  }

  .loading-overlay {
    color: white;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .loading-text {
    font-size: 1rem;
    color: #e5e7eb;
  }

  .error-overlay {
    color: white;
    text-align: center;
  }

  .error-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .error-message {
    font-size: 1rem;
    color: #fca5a5;
    margin-bottom: 1.5rem;
    max-width: 400px;
  }

  .retry-btn {
    padding: 0.75rem 1.5rem;
    border: 1px solid #3b82f6;
    border-radius: 6px;
    background: #3b82f6;
    color: white;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .retry-btn:hover {
    background: #2563eb;
    border-color: #2563eb;
  }

  .welcome-overlay {
    color: white;
    text-align: center;
  }

  .welcome-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
  }

  .welcome-overlay h2 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .welcome-overlay p {
    margin: 0 0 2rem 0;
    font-size: 1.125rem;
    color: #9ca3af;
  }

  .features {
    display: flex;
    gap: 2rem;
    justify-content: center;
  }

  .feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }

  .feature-icon {
    font-size: 2rem;
  }

  .feature span:last-child {
    font-size: 0.875rem;
    color: #d1d5db;
  }

  .canvas-overlay {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 5;
    pointer-events: none;
  }

  .mode-indicator {
    padding: 0.5rem 1rem;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 6px;
    backdrop-filter: blur(10px);
  }

  .mode-text {
    font-size: 0.875rem;
    color: white;
    font-weight: 500;
  }

  @media (max-width: 768px) {
    .features {
      flex-direction: column;
      gap: 1rem;
    }

    .welcome-overlay h2 {
      font-size: 1.5rem;
    }

    .welcome-overlay p {
      font-size: 1rem;
    }

    .canvas-overlay {
      top: 0.5rem;
      right: 0.5rem;
    }

    .mode-indicator {
      padding: 0.375rem 0.75rem;
    }

    .mode-text {
      font-size: 0.75rem;
    }
  }
</style>
