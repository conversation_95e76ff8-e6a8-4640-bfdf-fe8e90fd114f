{"rustc": 18201530525194187439, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 6916628722623291361, "deps": [[376837177317575824, "softbuffer", false, 1024909902817261910], [442785307232013896, "tauri_runtime", false, 2486231081682607093], [3150220818285335163, "url", false, 13229562903167965585], [3722963349756955755, "once_cell", false, 14558165606666595800], [4143744114649553716, "raw_window_handle", false, 15870071983738474809], [5986029879202738730, "log", false, 9926080830494016322], [7752760652095876438, "build_script_build", false, 12386219449965751964], [8539587424388551196, "webview2_com", false, 13289029005029646273], [9010263965687315507, "http", false, 11170670649357434941], [11050281405049894993, "tauri_utils", false, 7565535698388826477], [13223659721939363523, "tao", false, 4984236506959004012], [14585479307175734061, "windows", false, 18002416130416677670], [14794439852947137341, "wry", false, 454758728659903356]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-c59a36876f09a41f\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}