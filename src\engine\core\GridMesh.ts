/**
 * 网格类 - 管理图片的网格变形
 */

import { Container, Graphics, Texture, MeshSimple } from 'pixi.js';
import type { Point2D, GridMeshData, MeshVertex, Triangle, GridConfig } from '../types';
import { MathUtils } from '../utils/math';

export class GridMesh extends Container {
  private texture: Texture;
  private meshData: GridMeshData;
  private mesh: MeshSimple | null = null;
  private gridLines: Graphics;
  private config: GridConfig;
  private imageSize: { width: number; height: number };

  constructor(texture: Texture, config: GridConfig, imageSize: { width: number; height: number }) {
    super();

    this.texture = texture;
    this.config = config;
    this.imageSize = imageSize;

    console.log('Creating GridMesh with config:', config);
    console.log('Image size:', imageSize);

    // 初始化网格数据
    this.meshData = this.createGridMeshData();

    // 创建网格
    this.createMesh();

    // 创建网格线容器 - 放在网格之后，确保在上层
    this.gridLines = new Graphics();
    this.addChild(this.gridLines);

    // 绘制网格线
    this.updateGridLines();

    console.log('GridMesh created with', this.meshData.vertices.length, 'vertices');
  }

  /**
   * 创建网格数据
   */
  private createGridMeshData(): GridMeshData {
    const { rows, cols } = this.config;
    const { width, height } = this.imageSize;

    const vertices: MeshVertex[] = [];
    const triangles: Triangle[] = [];

    // 创建顶点
    for (let row = 0; row <= rows; row++) {
      for (let col = 0; col <= cols; col++) {
        const x = (col / cols) * width;
        const y = (row / rows) * height;
        const u = col / cols;
        const v = row / rows;

        vertices.push({
          position: { x, y },
          uv: { x: u, y: v },
          originalPosition: { x, y }
        });
      }
    }

    // 创建三角形
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const topLeft = row * (cols + 1) + col;
        const topRight = topLeft + 1;
        const bottomLeft = (row + 1) * (cols + 1) + col;
        const bottomRight = bottomLeft + 1;

        // 第一个三角形 (左上, 右上, 左下)
        triangles.push({
          vertices: [topLeft, topRight, bottomLeft]
        });

        // 第二个三角形 (右上, 右下, 左下)
        triangles.push({
          vertices: [topRight, bottomRight, bottomLeft]
        });
      }
    }

    return {
      vertices,
      triangles,
      rows,
      cols
    };
  }

  /**
   * 创建PIXI网格
   */
  private createMesh(): void {
    const vertices: number[] = [];
    const uvs: number[] = [];
    const indices: number[] = [];

    // 构建顶点数组
    this.meshData.vertices.forEach(vertex => {
      vertices.push(vertex.position.x, vertex.position.y);
      uvs.push(vertex.uv.x, vertex.uv.y);
    });

    // 构建索引数组
    this.meshData.triangles.forEach(triangle => {
      indices.push(...triangle.vertices);
    });

    // 移除旧网格
    if (this.mesh) {
      this.removeChild(this.mesh);
      this.mesh.destroy();
    }

    // 创建新网格 - 使用 PIXI.js v8 的 MeshSimple
    this.mesh = new MeshSimple({
      texture: this.texture,
      vertices: new Float32Array(vertices),
      uvs: new Float32Array(uvs),
      indices: new Uint32Array(indices)
    });

    // 如果网格线已经存在，确保网格在网格线下方
    if (this.gridLines) {
      this.addChildAt(this.mesh, 0); // 添加到底层
      // 确保网格线在最上层
      this.setChildIndex(this.gridLines, this.children.length - 1);
    } else {
      this.addChild(this.mesh);
    }
  }

  /**
   * 更新网格
   */
  public updateMesh(): void {
    if (!this.mesh) return;

    const vertices: number[] = [];

    // 更新顶点位置
    this.meshData.vertices.forEach(vertex => {
      vertices.push(vertex.position.x, vertex.position.y);
    });

    console.log('Updating mesh with vertices:', vertices.length / 2, 'points');
    console.log('First few vertices:', vertices.slice(0, 8));

    // 更新网格顶点 - PIXI.js v8 方式
    try {
      // 方法1: 直接更新 vertices 属性
      this.mesh.vertices = new Float32Array(vertices);

      // 方法2: 如果有 geometry 属性，尝试更新
      if (this.mesh.geometry) {
        // 标记几何体需要更新
        this.mesh.geometry.updateID = (this.mesh.geometry.updateID || 0) + 1;
      }

      // 方法3: 强制重新创建网格（如果上述方法不工作）
      if (!this.mesh.vertices || this.mesh.vertices.length !== vertices.length) {
        console.log('Recreating mesh due to vertex count mismatch');
        this.createMesh();
        return;
      }

    } catch (error) {
      console.warn('Failed to update mesh vertices, recreating mesh:', error);
      this.createMesh();
      return;
    }

    // 更新网格线
    this.updateGridLines();

    // 确保网格线在最上层
    if (this.gridLines) {
      this.setChildIndex(this.gridLines, this.children.length - 1);
    }

    console.log('Mesh updated successfully');
  }

  /**
   * 更新网格线显示
   */
  private updateGridLines(): void {
    this.gridLines.clear();

    if (!this.config.showGrid) {
      console.log('Grid display is disabled');
      return;
    }

    console.log('Drawing grid lines with config:', {
      color: this.config.gridColor.toString(16),
      alpha: this.config.gridAlpha,
      rows: this.config.rows,
      cols: this.config.cols
    });

    // 设置线条样式 - 使用更明显的样式
    this.gridLines.lineStyle({
      width: 3,
      color: this.config.gridColor,
      alpha: Math.max(this.config.gridAlpha, 0.8) // 确保至少有 0.8 的透明度
    });

    // 也可以尝试使用 stroke 方法
    this.gridLines.stroke({
      width: 3,
      color: this.config.gridColor,
      alpha: Math.max(this.config.gridAlpha, 0.8)
    });

    const { rows, cols } = this.config;
    let linesDrawn = 0;

    // 绘制水平线
    for (let row = 0; row <= rows; row++) {
      const startIndex = row * (cols + 1);
      const endIndex = startIndex + cols;

      const startVertex = this.meshData.vertices[startIndex];
      const endVertex = this.meshData.vertices[endIndex];

      if (row === 0) {
        console.log('First horizontal line:', {
          start: startVertex.position,
          end: endVertex.position
        });
      }

      this.gridLines.moveTo(startVertex.position.x, startVertex.position.y);
      this.gridLines.lineTo(endVertex.position.x, endVertex.position.y);
      linesDrawn++;
    }

    // 绘制垂直线
    for (let col = 0; col <= cols; col++) {
      for (let row = 0; row < rows; row++) {
        const currentIndex = row * (cols + 1) + col;
        const nextIndex = (row + 1) * (cols + 1) + col;

        const currentVertex = this.meshData.vertices[currentIndex];
        const nextVertex = this.meshData.vertices[nextIndex];

        this.gridLines.moveTo(currentVertex.position.x, currentVertex.position.y);
        this.gridLines.lineTo(nextVertex.position.x, nextVertex.position.y);
        linesDrawn++;
      }
    }

    console.log(`Drew ${linesDrawn} grid lines`);

    // 绘制一些测试线条来确保网格线容器工作
    this.gridLines.lineStyle({
      width: 5,
      color: 0xff0000, // 红色
      alpha: 1.0
    });

    // 绘制一个简单的十字形测试
    const centerX = this.imageSize.width / 2;
    const centerY = this.imageSize.height / 2;

    // 水平线
    this.gridLines.moveTo(0, centerY);
    this.gridLines.lineTo(this.imageSize.width, centerY);

    // 垂直线
    this.gridLines.moveTo(centerX, 0);
    this.gridLines.lineTo(centerX, this.imageSize.height);

    console.log('Drew test cross at center:', { centerX, centerY, imageSize: this.imageSize });

    // 确保网格线在最上层
    this.setChildIndex(this.gridLines, this.children.length - 1);
  }

  /**
   * 应用变形
   */
  public applyDeformation(controlPoints: Array<{ original: Point2D; current: Point2D; influence: number }>): void {
    // 使用RBF算法进行变形
    MathUtils.deformMeshRBF(this.meshData.vertices, controlPoints);

    // 更新网格显示
    this.updateMesh();
  }

  /**
   * 重置网格到原始状态
   */
  public reset(): void {
    this.meshData.vertices.forEach(vertex => {
      vertex.position.x = vertex.originalPosition.x;
      vertex.position.y = vertex.originalPosition.y;
    });

    this.updateMesh();
  }

  /**
   * 设置网格配置
   */
  public setConfig(config: Partial<GridConfig>): void {
    this.config = { ...this.config, ...config };

    // 如果行列数改变，重新创建网格
    if (config.rows !== undefined || config.cols !== undefined) {
      this.meshData = this.createGridMeshData();
      this.createMesh();
    }

    this.updateGridLines();
  }

  /**
   * 获取网格数据
   */
  public getMeshData(): GridMeshData {
    return {
      ...this.meshData,
      vertices: this.meshData.vertices.map(v => ({
        ...v,
        position: { ...v.position },
        uv: { ...v.uv },
        originalPosition: { ...v.originalPosition }
      }))
    };
  }

  /**
   * 获取指定位置最近的网格顶点
   */
  public getNearestVertex(position: Point2D): { vertex: MeshVertex; index: number; distance: number } | null {
    let nearestVertex: MeshVertex | null = null;
    let nearestIndex = -1;
    let minDistance = Infinity;

    this.meshData.vertices.forEach((vertex, index) => {
      const distance = MathUtils.distance(position, vertex.position);
      if (distance < minDistance) {
        minDistance = distance;
        nearestVertex = vertex;
        nearestIndex = index;
      }
    });

    return nearestVertex ? { vertex: nearestVertex, index: nearestIndex, distance: minDistance } : null;
  }

  /**
   * 销毁网格
   */
  public destroy(): void {
    if (this.mesh) {
      this.mesh.destroy();
    }
    this.gridLines.destroy();
    super.destroy();
  }
}
