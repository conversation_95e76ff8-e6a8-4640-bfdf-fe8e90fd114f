/**
 * 事件发射器基类
 */

import type { EventCallback } from '../types';

export class EventEmitter<T extends Record<string, any> = Record<string, any>> {
  private listeners: Map<keyof T, Set<EventCallback>> = new Map();

  /**
   * 添加事件监听器
   */
  on<K extends keyof T>(event: K, callback: EventCallback<T[K]>): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(callback);
  }

  /**
   * 移除事件监听器
   */
  off<K extends keyof T>(event: K, callback: EventCallback<T[K]>): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.delete(callback);
      if (callbacks.size === 0) {
        this.listeners.delete(event);
      }
    }
  }

  /**
   * 添加一次性事件监听器
   */
  once<K extends keyof T>(event: K, callback: EventCallback<T[K]>): void {
    const onceCallback = (data: T[K]) => {
      callback(data);
      this.off(event, onceCallback);
    };
    this.on(event, onceCallback);
  }

  /**
   * 触发事件
   */
  emit<K extends keyof T>(event: K, data: T[K]): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${String(event)}:`, error);
        }
      });
    }
  }

  /**
   * 移除所有监听器
   */
  removeAllListeners<K extends keyof T>(event?: K): void {
    if (event) {
      this.listeners.delete(event);
    } else {
      this.listeners.clear();
    }
  }

  /**
   * 获取事件的监听器数量
   */
  listenerCount<K extends keyof T>(event: K): number {
    const callbacks = this.listeners.get(event);
    return callbacks ? callbacks.size : 0;
  }

  /**
   * 获取所有事件名称
   */
  eventNames(): Array<keyof T> {
    return Array.from(this.listeners.keys());
  }
}
