{"rustc": 18201530525194187439, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 3097720439266052521, "deps": [[40386456601120721, "percent_encoding", false, 17841472273385781477], [442785307232013896, "tauri_runtime", false, 2486231081682607093], [1200537532907108615, "url<PERSON><PERSON>n", false, 16932822225406997495], [3150220818285335163, "url", false, 13229562903167965585], [4143744114649553716, "raw_window_handle", false, 15870071983738474809], [4341921533227644514, "muda", false, 1162617757376250555], [4919829919303820331, "serialize_to_javascript", false, 14148188722736508142], [5986029879202738730, "log", false, 9926080830494016322], [7752760652095876438, "tauri_runtime_wry", false, 16233117943801555106], [8539587424388551196, "webview2_com", false, 13289029005029646273], [9010263965687315507, "http", false, 11170670649357434941], [9228235415475680086, "tauri_macros", false, 402891798283617346], [9538054652646069845, "tokio", false, 17620342316557883759], [9689903380558560274, "serde", false, 4890963700188236560], [9920160576179037441, "getrandom", false, 5522515030407616940], [10229185211513642314, "mime", false, 6018312460980809107], [10629569228670356391, "futures_util", false, 2818275942918758338], [10755362358622467486, "build_script_build", false, 2604541221486670530], [10806645703491011684, "thiserror", false, 10195616998906548453], [11050281405049894993, "tauri_utils", false, 7565535698388826477], [11989259058781683633, "dunce", false, 9505378042092537662], [12565293087094287914, "window_vibrancy", false, 13665821419425015864], [12986574360607194341, "serde_repr", false, 15007053662919544837], [13077543566650298139, "heck", false, 6876235400383029375], [13625485746686963219, "anyhow", false, 6669425199327153378], [14585479307175734061, "windows", false, 18002416130416677670], [15367738274754116744, "serde_json", false, 8602307583832840759], [16928111194414003569, "dirs", false, 8428549405163334234], [17155886227862585100, "glob", false, 8439216362999676052]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-7dcede7f7f81d47f\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}