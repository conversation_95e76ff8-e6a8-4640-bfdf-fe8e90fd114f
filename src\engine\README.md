# 网格变形引擎 (Mesh Deformation Engine)

基于 PIXI.js 的图片网格变形引擎，支持添加控制点并通过拖拽实现图片变形效果。

## 功能特性

- 🎯 **交互式控制点** - 点击添加控制点，拖拽实现变形
- 🔧 **多种交互模式** - 添加点、移动点、选择等模式
- 🎨 **可视化网格** - 显示/隐藏网格线，自定义网格密度
- 🚀 **高性能渲染** - 基于 WebGL 的 PIXI.js 引擎
- 📐 **数学算法** - 径向基函数 (RBF) 变形算法
- 🎛️ **灵活配置** - 丰富的配置选项和事件系统

## 快速开始

### 1. 基础使用

```typescript
import { createMeshEngine, InteractionMode } from './engine';

// 创建引擎实例
const container = document.getElementById('canvas-container')!;
const engine = createMeshEngine(container, {
  width: 800,
  height: 600,
  grid: {
    rows: 8,
    cols: 8,
    showGrid: true
  }
});

// 初始化引擎
await engine.initialize();

// 加载图片
await engine.loadImage('/path/to/your/image.jpg');

// 设置为添加控制点模式
engine.setInteractionMode(InteractionMode.ADD_POINT);
```

### 2. 事件监听

```typescript
// 监听控制点添加
engine.on('point-added', (data) => {
  console.log('控制点已添加:', data.point);
});

// 监听网格更新
engine.on('mesh-updated', (data) => {
  console.log('网格已更新:', data.meshData);
});

// 监听交互模式变化
engine.on('interaction-mode-changed', (data) => {
  console.log('交互模式已变更:', data.mode);
});
```

### 3. 交互模式

```typescript
// 添加控制点模式
engine.setInteractionMode(InteractionMode.ADD_POINT);

// 移动控制点模式
engine.setInteractionMode(InteractionMode.MOVE_POINT);

// 选择模式
engine.setInteractionMode(InteractionMode.SELECT);

// 无交互模式
engine.setInteractionMode(InteractionMode.NONE);
```

### 4. 控制点操作

```typescript
// 手动添加控制点
const point = engine.addControlPoint({ x: 100, y: 100 }, 150);

// 获取所有控制点
const allPoints = engine.getAllControlPoints();

// 获取选中的控制点
const selectedPoints = engine.getSelectedPoints();

// 移除控制点
engine.removeControlPoint(point.id);

// 清除所有控制点
engine.clearAllControlPoints();
```

### 5. 网格配置

```typescript
// 更新网格配置
engine.setGridConfig({
  rows: 12,
  cols: 12,
  showGrid: false,
  gridColor: 0x0000ff,
  gridAlpha: 0.5
});

// 重置网格
engine.resetMesh();
```

### 6. 数据导入导出

```typescript
// 导出当前状态
const data = engine.exportMeshData();

// 导入状态
engine.importMeshData(data);
```

## 配置选项

### MeshEngineConfig

```typescript
interface MeshEngineConfig {
  container: HTMLElement;        // DOM 容器
  width: number;                 // 画布宽度
  height: number;                // 画布高度
  backgroundColor: number;       // 背景色
  grid: GridConfig;             // 网格配置
  controlPoint: ControlPointConfig; // 控制点配置
  enableInteraction: boolean;    // 是否启用交互
}
```

### GridConfig

```typescript
interface GridConfig {
  rows: number;        // 网格行数
  cols: number;        // 网格列数
  showGrid: boolean;   // 是否显示网格线
  gridColor: number;   // 网格线颜色
  gridAlpha: number;   // 网格线透明度
}
```

### ControlPointConfig

```typescript
interface ControlPointConfig {
  radius: number;         // 控制点半径
  color: number;          // 默认颜色
  hoverColor: number;     // 悬停颜色
  selectedColor: number;  // 选中颜色
  strokeWidth: number;    // 描边宽度
  strokeColor: number;    // 描边颜色
}
```

## 事件系统

### 可监听事件

- `point-added` - 控制点添加
- `point-removed` - 控制点移除
- `point-moved` - 控制点移动
- `point-selected` - 控制点选中
- `mesh-updated` - 网格更新
- `interaction-mode-changed` - 交互模式变化

## 架构设计

```
MeshEngine (主引擎)
├── GridMesh (网格管理)
├── InteractionManager (交互管理)
├── ControlPoint (控制点)
├── EventEmitter (事件系统)
└── MathUtils (数学工具)
```

## 算法说明

### 径向基函数 (RBF) 变形

引擎使用径向基函数算法实现平滑的图像变形：

1. **权重计算**: 根据距离计算每个控制点的影响权重
2. **位移插值**: 使用权重对位移进行插值
3. **顶点更新**: 更新网格顶点位置
4. **实时渲染**: 通过 PIXI.js 实时渲染变形结果

### 三角剖分网格

- 将图像分割为三角形网格
- 每个顶点可独立变形
- 保持纹理坐标映射
- 支持实时更新

## 性能优化

- 使用 WebGL 硬件加速
- 对象池管理控制点
- 增量更新网格顶点
- 事件防抖处理

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

需要支持 WebGL 的现代浏览器。

## 许可证

MIT License
