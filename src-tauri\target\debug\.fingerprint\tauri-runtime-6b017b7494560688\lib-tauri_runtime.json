{"rustc": 18201530525194187439, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 11307565108468277508, "deps": [[442785307232013896, "build_script_build", false, 6565704694030501320], [3150220818285335163, "url", false, 13229562903167965585], [4143744114649553716, "raw_window_handle", false, 15870071983738474809], [7606335748176206944, "dpi", false, 11726889538687311593], [9010263965687315507, "http", false, 11170670649357434941], [9689903380558560274, "serde", false, 4890963700188236560], [10806645703491011684, "thiserror", false, 10195616998906548453], [11050281405049894993, "tauri_utils", false, 7565535698388826477], [14585479307175734061, "windows", false, 18002416130416677670], [15367738274754116744, "serde_json", false, 8602307583832840759], [16727543399706004146, "cookie", false, 15043811414586383313]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-6b017b7494560688\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}