{"rustc": 18201530525194187439, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 8511438651892254895, "deps": [[3060637413840920116, "proc_macro2", false, 11271011234119958394], [4974441333307933176, "syn", false, 13538254114649996720], [7341521034400937459, "tauri_codegen", false, 3035381938033962854], [11050281405049894993, "tauri_utils", false, 9245694419520344700], [13077543566650298139, "heck", false, 6876235400383029375], [17990358020177143287, "quote", false, 2475592046853697604]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-09bad78e4d203e4b\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}