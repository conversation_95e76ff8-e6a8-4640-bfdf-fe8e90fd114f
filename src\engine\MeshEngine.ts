/**
 * 网格变形引擎主类
 */

import * as PIXI from 'pixi.js';
import type {
  MeshEngineConfig,
  MeshEngineEvents,
  InteractionMode,
  Point2D,
  DeformationOptions
} from './types';
import { EventEmitter } from './core/EventEmitter';
import { GridMesh } from './core/GridMesh';
import { InteractionManager } from './core/InteractionManager';
import { ControlPoint } from './core/ControlPoint';

export class MeshEngine extends EventEmitter<MeshEngineEvents> {
  private app: PIXI.Application | null = null;
  private config: MeshEngineConfig;
  private gridMesh: GridMesh | null = null;
  private interactionManager: InteractionManager | null = null;
  private mainContainer: PIXI.Container | null = null;
  private isInitialized: boolean = false;

  constructor(config: MeshEngineConfig) {
    super();
    this.config = config;
  }

  /**
   * 初始化引擎
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.warn('MeshEngine is already initialized');
      return;
    }

    try {
      // 创建PIXI应用
      this.app = new PIXI.Application();

      // 初始化PIXI应用
      await this.app.init({
        width: this.config.width,
        height: this.config.height,
        backgroundColor: this.config.backgroundColor,
        antialias: true,
        resolution: window.devicePixelRatio || 1,
        autoDensity: true
      });

      // 创建主容器
      this.mainContainer = new PIXI.Container();

      // 将canvas添加到DOM
      this.config.container.appendChild(this.app.canvas);

      // 添加主容器到舞台
      this.app.stage.addChild(this.mainContainer);

      // 创建交互管理器
      this.interactionManager = new InteractionManager(
        this.app,
        this.mainContainer,
        this.config.controlPoint
      );

      // 设置事件监听器
      this.setupEventListeners();

      this.isInitialized = true;

      console.log('MeshEngine initialized successfully');
    } catch (error) {
      console.error('Failed to initialize MeshEngine:', error);
      throw error;
    }
  }

  /**
   * 加载图片并创建网格
   */
  public async loadImage(imageUrl: string): Promise<void> {
    if (!this.isInitialized || !this.app || !this.mainContainer) {
      throw new Error('MeshEngine must be initialized before loading images');
    }

    try {
      // 加载纹理
      const texture = await PIXI.Assets.load(imageUrl);

      // 计算图片尺寸（保持宽高比）
      const imageSize = this.calculateImageSize(texture.width, texture.height);

      // 移除旧的网格
      if (this.gridMesh) {
        this.mainContainer.removeChild(this.gridMesh);
        this.gridMesh.destroy();
      }

      // 创建新的网格
      this.gridMesh = new GridMesh(texture, this.config.grid, imageSize);
      this.mainContainer.addChildAt(this.gridMesh, 0); // 添加到底层

      // 居中显示
      this.centerMesh();

      console.log('Image loaded and mesh created successfully');
    } catch (error) {
      console.error('Failed to load image:', error);
      throw error;
    }
  }

  /**
   * 计算图片显示尺寸
   */
  private calculateImageSize(originalWidth: number, originalHeight: number): { width: number; height: number } {
    const maxWidth = this.config.width * 0.8; // 留出边距
    const maxHeight = this.config.height * 0.8;

    const scaleX = maxWidth / originalWidth;
    const scaleY = maxHeight / originalHeight;
    const scale = Math.min(scaleX, scaleY);

    return {
      width: originalWidth * scale,
      height: originalHeight * scale
    };
  }

  /**
   * 居中网格
   */
  private centerMesh(): void {
    if (!this.gridMesh) return;

    const bounds = this.gridMesh.getBounds();
    this.gridMesh.x = (this.config.width - bounds.width) / 2;
    this.gridMesh.y = (this.config.height - bounds.height) / 2;
  }

  /**
   * 设置交互模式
   */
  public setInteractionMode(mode: InteractionMode): void {
    this.interactionManager.setMode(mode);
    this.emit('interaction-mode-changed', { mode });
  }

  /**
   * 添加控制点
   */
  public addControlPoint(position: Point2D, influenceRadius: number = 100): ControlPoint {
    return this.interactionManager.addControlPoint(position, influenceRadius);
  }

  /**
   * 移除控制点
   */
  public removeControlPoint(pointId: string): boolean {
    return this.interactionManager.removeControlPoint(pointId);
  }

  /**
   * 应用变形
   */
  public applyDeformation(options?: DeformationOptions): void {
    if (!this.gridMesh) {
      console.warn('No mesh available for deformation');
      return;
    }

    const controlPoints = this.interactionManager.getAllPoints();
    const deformationData = controlPoints.map(point => ({
      original: point.data.originalPosition,
      current: point.data.position,
      influence: point.data.influenceRadius
    }));

    this.gridMesh.applyDeformation(deformationData);

    this.emit('mesh-updated', {
      meshData: this.gridMesh.getMeshData()
    });
  }

  /**
   * 重置网格
   */
  public resetMesh(): void {
    if (this.gridMesh) {
      this.gridMesh.reset();
    }

    // 重置所有控制点
    this.interactionManager.getAllPoints().forEach(point => {
      point.reset();
    });

    this.emit('mesh-updated', {
      meshData: this.gridMesh?.getMeshData() || {} as any
    });
  }

  /**
   * 设置网格配置
   */
  public setGridConfig(config: Partial<typeof this.config.grid>): void {
    this.config.grid = { ...this.config.grid, ...config };

    if (this.gridMesh) {
      this.gridMesh.setConfig(this.config.grid);
    }
  }

  /**
   * 获取当前选中的控制点
   */
  public getSelectedPoints(): ControlPoint[] {
    return this.interactionManager.getSelectedPoints();
  }

  /**
   * 获取所有控制点
   */
  public getAllControlPoints(): ControlPoint[] {
    return this.interactionManager.getAllPoints();
  }

  /**
   * 清除所有控制点
   */
  public clearAllControlPoints(): void {
    const points = this.interactionManager.getAllPoints();
    points.forEach(point => {
      this.interactionManager.removeControlPoint(point.id);
    });
  }

  /**
   * 导出网格数据
   */
  public exportMeshData(): any {
    if (!this.gridMesh) return null;

    return {
      meshData: this.gridMesh.getMeshData(),
      controlPoints: this.interactionManager.getAllPoints().map(point => point.getData()),
      config: this.config
    };
  }

  /**
   * 导入网格数据
   */
  public importMeshData(data: any): void {
    // 清除现有控制点
    this.clearAllControlPoints();

    // 重新创建控制点
    if (data.controlPoints) {
      data.controlPoints.forEach((pointData: any) => {
        const point = this.addControlPoint(pointData.position, pointData.influenceRadius);
        point.setPosition(pointData.position);
      });
    }

    // 应用变形
    this.applyDeformation();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听交互管理器事件
    this.interactionManager.on('point-added', (data) => {
      this.emit('point-added', { point: data.point.getData() });
      // 自动应用变形
      this.applyDeformation();
    });

    this.interactionManager.on('point-selected', (data) => {
      this.emit('point-selected', { point: data.point.getData() });
    });

    this.interactionManager.on('selection-changed', () => {
      // 当选择改变时应用变形
      this.applyDeformation();
    });

    this.interactionManager.on('mode-changed', (data) => {
      this.emit('interaction-mode-changed', { mode: data.mode });
    });
  }

  /**
   * 调整画布大小
   */
  public resize(width: number, height: number): void {
    this.config.width = width;
    this.config.height = height;

    this.app.renderer.resize(width, height);

    // 重新居中网格
    this.centerMesh();
  }

  /**
   * 销毁引擎
   */
  public destroy(): void {
    // 销毁交互管理器
    this.interactionManager.destroy();

    // 销毁网格
    if (this.gridMesh) {
      this.gridMesh.destroy();
    }

    // 销毁PIXI应用
    this.app.destroy(true, { children: true, texture: true });

    // 移除事件监听器
    this.removeAllListeners();

    this.isInitialized = false;

    console.log('MeshEngine destroyed');
  }

  /**
   * 获取当前交互模式
   */
  public getInteractionMode(): InteractionMode {
    return this.interactionManager.getMode();
  }

  /**
   * 检查引擎是否已初始化
   */
  public isReady(): boolean {
    return this.isInitialized;
  }
}
