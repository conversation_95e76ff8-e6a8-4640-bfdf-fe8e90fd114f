/**
 * 数学工具函数
 */

import type { Point2D, MeshVertex } from '../types';

export class MathUtils {
  /**
   * 计算两点之间的距离
   */
  static distance(p1: Point2D, p2: Point2D): number {
    const dx = p1.x - p2.x;
    const dy = p1.y - p2.y;
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * 计算两点之间的距离的平方（避免开方运算）
   */
  static distanceSquared(p1: Point2D, p2: Point2D): number {
    const dx = p1.x - p2.x;
    const dy = p1.y - p2.y;
    return dx * dx + dy * dy;
  }

  /**
   * 线性插值
   */
  static lerp(a: number, b: number, t: number): number {
    return a + (b - a) * t;
  }

  /**
   * 点的线性插值
   */
  static lerpPoint(p1: Point2D, p2: Point2D, t: number): Point2D {
    return {
      x: this.lerp(p1.x, p2.x, t),
      y: this.lerp(p1.y, p2.y, t)
    };
  }

  /**
   * 限制值在指定范围内
   */
  static clamp(value: number, min: number, max: number): number {
    return Math.min(Math.max(value, min), max);
  }

  /**
   * 将值从一个范围映射到另一个范围
   */
  static map(value: number, fromMin: number, fromMax: number, toMin: number, toMax: number): number {
    const ratio = (value - fromMin) / (fromMax - fromMin);
    return toMin + ratio * (toMax - toMin);
  }

  /**
   * 计算径向基函数 (RBF) 权重
   */
  static rbfWeight(distance: number, radius: number): number {
    if (distance >= radius) return 0;
    const t = distance / radius;
    // 使用三次多项式核函数
    return Math.pow(1 - t, 3);
  }

  /**
   * 计算双线性插值
   */
  static bilinearInterpolation(
    p00: number, p10: number, p01: number, p11: number,
    tx: number, ty: number
  ): number {
    const a = this.lerp(p00, p10, tx);
    const b = this.lerp(p01, p11, tx);
    return this.lerp(a, b, ty);
  }

  /**
   * 计算点在三角形内的重心坐标
   */
  static barycentricCoordinates(
    point: Point2D,
    v0: Point2D,
    v1: Point2D,
    v2: Point2D
  ): { u: number; v: number; w: number } {
    const v0v1 = { x: v1.x - v0.x, y: v1.y - v0.y };
    const v0v2 = { x: v2.x - v0.x, y: v2.y - v0.y };
    const v0p = { x: point.x - v0.x, y: point.y - v0.y };

    const dot00 = v0v2.x * v0v2.x + v0v2.y * v0v2.y;
    const dot01 = v0v2.x * v0v1.x + v0v2.y * v0v1.y;
    const dot02 = v0v2.x * v0p.x + v0v2.y * v0p.y;
    const dot11 = v0v1.x * v0v1.x + v0v1.y * v0v1.y;
    const dot12 = v0v1.x * v0p.x + v0v1.y * v0p.y;

    const invDenom = 1 / (dot00 * dot11 - dot01 * dot01);
    const u = (dot11 * dot02 - dot01 * dot12) * invDenom;
    const v = (dot00 * dot12 - dot01 * dot02) * invDenom;
    const w = 1 - u - v;

    return { u, v, w };
  }

  /**
   * 检查点是否在三角形内
   */
  static isPointInTriangle(point: Point2D, v0: Point2D, v1: Point2D, v2: Point2D): boolean {
    const { u, v, w } = this.barycentricCoordinates(point, v0, v1, v2);
    return u >= 0 && v >= 0 && w >= 0;
  }

  /**
   * 计算网格顶点的变形
   * 使用径向基函数进行平滑变形
   */
  static deformMeshRBF(
    vertices: MeshVertex[],
    controlPoints: Array<{ original: Point2D; current: Point2D; influence: number }>
  ): void {
    console.log('Deforming mesh with', vertices.length, 'vertices and', controlPoints.length, 'control points');

    // 打印控制点详细信息
    controlPoints.forEach((cp, i) => {
      const dx = cp.current.x - cp.original.x;
      const dy = cp.current.y - cp.original.y;
      console.log(`Control Point ${i}:`);
      console.log(`  Original: (${cp.original.x.toFixed(2)}, ${cp.original.y.toFixed(2)})`);
      console.log(`  Current: (${cp.current.x.toFixed(2)}, ${cp.current.y.toFixed(2)})`);
      console.log(`  Displacement: (${dx.toFixed(2)}, ${dy.toFixed(2)})`);
      console.log(`  Influence: ${cp.influence}`);
    });

    // 打印前几个网格顶点的位置
    console.log('First few mesh vertices:');
    for (let i = 0; i < Math.min(5, vertices.length); i++) {
      console.log(`Vertex ${i}:`, vertices[i].originalPosition);
    }

    // 检查控制点是否有位移
    const hasDisplacement = controlPoints.some(cp => {
      const dx = cp.current.x - cp.original.x;
      const dy = cp.current.y - cp.original.y;
      return Math.abs(dx) > 0.1 || Math.abs(dy) > 0.1;
    });

    if (!hasDisplacement) {
      console.log('No control points have significant displacement');
      return;
    }

    let deformedCount = 0;
    let maxWeight = 0;

    for (let i = 0; i < vertices.length; i++) {
      const vertex = vertices[i];
      let totalWeight = 0;
      let displacement = { x: 0, y: 0 };

      for (const cp of controlPoints) {
        const distance = this.distance(vertex.originalPosition, cp.original);
        const weight = this.rbfWeight(distance, cp.influence);

        maxWeight = Math.max(maxWeight, weight);

        if (weight > 0) {
          const dx = cp.current.x - cp.original.x;
          const dy = cp.current.y - cp.original.y;

          displacement.x += dx * weight;
          displacement.y += dy * weight;
          totalWeight += weight;

          // 调试第一个受影响的顶点
          if (i === 0 && weight > 0) {
            console.log('First vertex affected:', {
              vertexPos: vertex.originalPosition,
              cpOriginal: cp.original,
              cpCurrent: cp.current,
              distance: distance.toFixed(2),
              weight: weight.toFixed(4),
              influence: cp.influence,
              displacement: { dx: dx.toFixed(2), dy: dy.toFixed(2) }
            });
          }
        } else if (i === 0) {
          // 调试第一个顶点为什么没有受影响
          console.log(`First vertex not affected by CP ${controlPoints.indexOf(cp)}:`, {
            vertexPos: vertex.originalPosition,
            cpOriginal: cp.original,
            distance: distance.toFixed(2),
            influence: cp.influence,
            weight: weight.toFixed(4)
          });
        }
      }

      const oldX = vertex.position.x;
      const oldY = vertex.position.y;

      if (totalWeight > 0) {
        vertex.position.x = vertex.originalPosition.x + displacement.x / totalWeight;
        vertex.position.y = vertex.originalPosition.y + displacement.y / totalWeight;

        if (Math.abs(vertex.position.x - oldX) > 0.1 || Math.abs(vertex.position.y - oldY) > 0.1) {
          deformedCount++;
        }
      } else {
        vertex.position.x = vertex.originalPosition.x;
        vertex.position.y = vertex.originalPosition.y;
      }
    }

    console.log('Deformed', deformedCount, 'vertices, max weight:', maxWeight);
  }

  /**
   * 生成唯一ID
   */
  static generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  /**
   * 角度转弧度
   */
  static degToRad(degrees: number): number {
    return degrees * Math.PI / 180;
  }

  /**
   * 弧度转角度
   */
  static radToDeg(radians: number): number {
    return radians * 180 / Math.PI;
  }
}
